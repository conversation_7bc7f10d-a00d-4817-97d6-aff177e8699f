"use client";

import { useState } from "react";
import LocationMap from "./LocationMap";
import { Property } from "./Property";

interface EditablePropertyLocationProps {
  property: Property;
}

export default function EditablePropertyLocation({ property }: EditablePropertyLocationProps) {
  const [latitude] = useState(property.latitude);
  const [longitude] = useState(property.longitude);

  return (
    <div className="mb-8">
      <div className="mb-4">
        <h2 className="text-2xl font-semibold text-gray-900">موقع العقار</h2>
      </div>

      <div className="space-y-4">
        <LocationMap
          latitude={latitude}
          longitude={longitude}
          onLocationSelect={() => {}} // No-op for display mode
          height="h-96"
          isSelectable={false}
        />
        {latitude && longitude && (
          <div className="text-sm text-gray-600">
            إحداثيات الموقع: {latitude.toFixed(6)}, {longitude.toFixed(6)}
          </div>
        )}
      </div>
    </div>
  );
}
