# GPS Location Feature for Dar Casa

## Overview

This feature adds GPS location functionality to the property management system, allowing users to specify exact coordinates for each property using an interactive map.

## Features Added

### 1. Property Type Updates

- Added `latitude` and `longitude` fields to the Property type
- Both fields are optional to maintain backward compatibility

### 2. Map Components

#### LocationMap Component (`src/components/LocationMap.tsx`)

- Interactive map for selecting property locations
- Click on the map to set GPS coordinates
- Blue marker shows the selected location
- Default center is Palestine (31.9522, 35.2332)
- Supports custom height and selectable mode

#### PropertyLocationMap Component (`src/components/PropertyLocationMap.tsx`)

- Display-only map showing property location
- Red marker indicates the property location
- Popup shows property title and coordinates
- Graceful fallback when no coordinates are provided

### 3. Form Integration

#### AddPropertyForm Updates

- Added map section for location selection
- Shows selected coordinates below the map
- Coordinates are saved with the property data

#### EditPropertyForm Updates

- Added map section for location editing
- Pre-populates with existing coordinates
- Allows updating location by clicking on map

### 4. Property Details Page

- Added "موقع العقار" (Property Location) section
- Displays property location on a map with red marker
- Shows property title in popup when clicking marker

## Technical Implementation

### Dependencies Added

- `leaflet`: Core mapping library
- `react-leaflet`: React components for Leaflet
- `@types/leaflet`: TypeScript definitions

### Key Features

- **SSR Safe**: Uses dynamic imports to avoid server-side rendering issues
- **Custom Markers**: Red marker for property display, blue marker for selection
- **Responsive Design**: Maps adapt to different screen sizes
- **Arabic Support**: All UI text is in Arabic
- **Error Handling**: Graceful fallbacks when coordinates are missing

### Map Configuration

- **Tile Provider**: OpenStreetMap (free, no API key required)
- **Default Zoom**: 13 for selection, 15 for display
- **Default Center**: Palestine coordinates
- **Marker Icons**: Custom SVG-based markers

## Usage

### For Property Owners

1. **Adding a Property**:

   - Fill in property details
   - Click on the map to set the exact location
   - Coordinates will be displayed below the map
   - Submit the form

2. **Editing a Property**:
   - Navigate to edit form
   - Click on map to update location
   - Save changes

### For Property Viewers

1. **Viewing Property Location**:
   - Navigate to property details page
   - Scroll to "موقع العقار" section
   - View the property location on the map
   - Click the red marker for property details

## File Structure

```
src/
├── components/
│   ├── LocationMap.tsx           # Interactive map for location selection
│   ├── PropertyLocationMap.tsx   # Display-only map for property location
│   ├── AddPropertyForm.tsx       # Updated with map integration
│   ├── EditPropertyForm.tsx      # Updated with map integration
│   └── Property.ts               # Updated type definition
├── app/
│   └── property/[id]/page.tsx    # Updated with location display
├── services/
│   └── propertyService.ts        # Updated to handle GPS coordinates
└── app/
    └── globals.css               # Added Leaflet CSS imports
```

## Browser Compatibility

- Modern browsers with ES6+ support
- Requires JavaScript enabled
- Responsive design for mobile and desktop

## Future Enhancements

- Geocoding support (address to coordinates)
- Reverse geocoding (coordinates to address)
- Multiple marker support for large properties
- Integration with external mapping services
- Location-based property search
