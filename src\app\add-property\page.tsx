"use client";

import { useRouter } from "next/navigation";
import { useEffect } from "react";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import AddPropertyForm from "@/components/AddPropertyForm";
import { useAuth } from "@/contexts/AuthContext";

export default function AddPropertyPage() {
  const router = useRouter();
  const { user, loading } = useAuth();

  // Redirect to signin if not authenticated
  useEffect(() => {
    if (!loading && !user) {
      router.push("/signin");
    }
  }, [user, loading, router]);

  const handleSuccess = () => {
    // Redirect to dashboard after successful addition
    router.push("/dashboard");
  };

  const handleClose = () => {
    router.push("/dashboard");
  };

  // Show loading or redirect if not authenticated
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4"></div>
          <p className="text-gray-600">جاري التحميل...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return null; // Will redirect to signin
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center mb-8">
          <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">إضافة عقار جديد</h1>
          <p className="text-xl text-gray-600">أضف عقارك الجديد إلى منصتنا</p>
        </div>
      </div>

      <AddPropertyForm onClose={handleClose} onSuccess={handleSuccess} />

      <Footer />
    </div>
  );
}
