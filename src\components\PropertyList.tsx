"use client";

import { useState } from "react";
import { Property } from "./Property";
import PropertyCard from "./PropertyCard";

interface PropertyListProps {
  properties: Property[];
}

export default function PropertyList({ properties }: PropertyListProps) {
  const [filter, setFilter] = useState("all");

  const filteredProperties = filter === "all" ? properties : properties.filter((property) => property.type === filter);

  return (
    <section className="py-16 bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">عقاراتنا</h2>
          <p className="text-lg text-gray-600 dark:text-gray-300">اكتشف أفضل العقارات المتاحة لدينا</p>
        </div>

        {/* Filter <PERSON> */}
        <div className="flex flex-wrap justify-center gap-4 mb-12">
          <button
            onClick={() => setFilter("all")}
            className={`px-6 py-3 rounded-full font-medium transition-colors ${
              filter === "all" ? "bg-green-600 text-white" : "bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600"
            }`}
          >
            جميع العقارات
          </button>
          <button
            onClick={() => setFilter("villa")}
            className={`px-6 py-3 rounded-full font-medium transition-colors ${
              filter === "villa" ? "bg-green-600 text-white" : "bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600"
            }`}
          >
            فيلات
          </button>
          <button
            onClick={() => setFilter("apartment")}
            className={`px-6 py-3 rounded-full font-medium transition-colors ${
              filter === "apartment" ? "bg-green-600 text-white" : "bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600"
            }`}
          >
            شقق
          </button>
          <button
            onClick={() => setFilter("office")}
            className={`px-6 py-3 rounded-full font-medium transition-colors ${
              filter === "office" ? "bg-green-600 text-white" : "bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600"
            }`}
          >
            مكاتب
          </button>
          <button
            onClick={() => setFilter("land")}
            className={`px-6 py-3 rounded-full font-medium transition-colors ${
              filter === "land" ? "bg-green-600 text-white" : "bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600"
            }`}
          >
            أراضي
          </button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {filteredProperties.map((property, index) => (
            <PropertyCard key={property.nid} property={property} variant="default" showTypeBadge={true} showFeaturedBadge={true} showPropertyDetails={true} index={index} />
          ))}
        </div>
      </div>
    </section>
  );
}
