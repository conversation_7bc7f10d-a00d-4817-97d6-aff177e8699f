# Dar Casa Project Structure Guide

## Project Overview

Dar Casa is a Next.js 15 real estate platform built with TypeScript, Tailwind CSS, and Firebase. The project follows the App Router pattern and includes Arabic RTL support.

## Key Entry Points

- Main layout: [src/app/layout.tsx](mdc:src/app/layout.tsx) - Root layout with RTL support and context providers
- Home page: [src/app/page.tsx](mdc:src/app/page.tsx) - Landing page with property listings
- Dashboard: [src/app/dashboard/page.tsx](mdc:src/app/dashboard/page.tsx) - User dashboard
- Property details: [src/app/property/[id]/page.tsx](mdc:src/app/property/[id]/page.tsx) - Individual property pages

## Core Architecture

- **App Router**: Uses Next.js 15 App Router with TypeScript
- **Styling**: Tailwind CSS with custom theme support
- **State Management**: React Context for auth and theme
- **Database**: Firebase Firestore for data storage
- **Authentication**: Firebase Auth
- **File Storage**: Firebase Storage for property images
- **Maps**: Leaflet with react-leaflet for location features

## Directory Structure

- `src/app/` - Next.js App Router pages and layouts
- `src/components/` - Reusable React components
- `src/contexts/` - React Context providers (Auth, Theme)
- `src/lib/` - Firebase configuration and utilities
- `src/services/` - API and service layer functions
- `src/utils/` - Helper functions and utilities

## Key Components

- Property type definition: [src/components/Property.ts](mdc:src/components/Property.ts)
- Firebase config: [src/lib/firebase.ts](mdc:src/lib/firebase.ts)
- Auth context: [src/contexts/AuthContext.tsx](mdc:src/contexts/AuthContext.tsx)
- Theme context: [src/contexts/ThemeContext.tsx](mdc:src/contexts/ThemeContext.tsx)

## Configuration Files

- TypeScript config: [tsconfig.json](mdc:tsconfig.json) - Path aliases configured with `@/*`
- Package config: [package.json](mdc:package.json) - Dependencies and scripts
- Tailwind config: [tailwind.config.ts](mdc:tailwind.config.ts) - Custom theme configuration
- Next.js config: [next.config.ts](mdc:next.config.ts) - Next.js specific settings
  description:
  globs:
  alwaysApply: false

---
