"use client";

import { useState, useEffect, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { X, Minimize2, MessageCircle, Bo<PERSON> } from "lucide-react";
import ChatMessage from "./ChatMessage";
import ChatInput from "./ChatInput";
import PropertySuggestions from "./PropertySuggestions";
import chatService from "@/services/chatService";
import { ChatState, PropertyMatch } from "@/types/chat";
import { Property } from "@/components/Property";
import "./chatbot.css";

interface ChatbotWidgetProps {
  availableProperties: Property[];
}

export default function ChatbotWidget({
  availableProperties,
}: ChatbotWidgetProps) {
  const [chatState, setChatState] = useState<ChatState>(chatService.getState());
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [isMinimized, setIsMinimized] = useState(false);

  useEffect(() => {
    // Subscribe to chat state changes
    const unsubscribe = chatService.subscribe(setChatState);

    // Initialize chat if it's the first time
    if (chatState.messages.length === 0) {
      chatService.initializeChat();
    }

    return unsubscribe;
  }, []);

  // Prevent background scrolling when chat is open
  useEffect(() => {
    if (chatState.isOpen) {
      document.body.classList.add("chatbot-open");
    } else {
      document.body.classList.remove("chatbot-open");
    }

    return () => {
      document.body.classList.remove("chatbot-open");
    };
  }, [chatState.isOpen]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [chatState.messages]);

  const handleSendMessage = async (message: string) => {
    await chatService.sendMessage(message, availableProperties);
  };

  const handleQuestionClick = async (question: string) => {
    await chatService.sendMessage(question, availableProperties);
  };

  const handlePropertySelect = (propertyMatch: PropertyMatch) => {
    // You can add logic here to highlight the property in the main view
    // or navigate to the property details
    console.log("Selected property:", propertyMatch);
  };

  const toggleMinimize = () => {
    setIsMinimized(!isMinimized);
  };

  const clearChat = () => {
    chatService.clearChat();
  };

  if (isMinimized) {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        className="fixed bottom-6 right-6 z-50"
      >
        <button
          onClick={toggleMinimize}
          className="bg-green-600 hover:bg-green-700 text-white p-4 rounded-full shadow-lg transition-all duration-200 hover:scale-110"
        >
          <MessageCircle className="w-6 h-6" />
        </button>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 100, scale: 0.9 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      exit={{ opacity: 0, y: 100, scale: 0.9 }}
      className="fixed bottom-4 right-4 md:bottom-6 md:right-6 z-50 w-full max-w-sm md:max-w-lg lg:max-w-xl h-[85vh] md:h-[700px] max-h-[800px] bg-white dark:bg-gray-900 rounded-2xl shadow-2xl border border-gray-200 dark:border-gray-700 flex flex-col chatbot-widget mx-4 md:mx-0"
    >
      {/* Header */}
      <div className="bg-gradient-to-r from-green-600 to-green-700 text-white p-3 md:p-4 rounded-t-2xl flex items-center justify-between flex-shrink-0">
        <div className="flex items-center space-x-3 space-x-reverse">
          <div className="w-8 h-8 md:w-10 md:h-10 bg-white/20 rounded-full flex items-center justify-center">
            <Bot className="w-4 h-4 md:w-6 md:h-6" />
          </div>
          <div>
            <h3 className="font-semibold text-sm md:text-base">
              مساعدك الذكي للعقارات
            </h3>
            <p className="text-xs text-green-100 hidden md:block">
              AI Property Assistant
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-1 space-x-reverse">
          <button
            onClick={toggleMinimize}
            className="p-1.5 md:p-2 hover:bg-white/20 rounded-lg transition-colors"
          >
            <Minimize2 className="w-4 h-4" />
          </button>
          <button
            onClick={() => chatService.closeChat()}
            className="p-1.5 md:p-2 hover:bg-white/20 rounded-lg transition-colors"
          >
            <X className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* Messages Area */}
      <div className="flex-1 overflow-y-auto p-3 md:p-4 space-y-3 bg-gray-50 dark:bg-gray-800 scrollbar-hide">
        <AnimatePresence>
          {chatState.messages.map((message, index) => (
            <ChatMessage
              key={message.id}
              message={message}
              isLast={index === chatState.messages.length - 1}
            />
          ))}
        </AnimatePresence>

        {/* Loading indicator */}
        {chatState.isLoading && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="flex items-center space-x-2 space-x-reverse text-gray-500 dark:text-gray-400"
          >
            <div className="flex space-x-1 space-x-reverse">
              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
              <div
                className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                style={{ animationDelay: "0.1s" }}
              ></div>
              <div
                className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                style={{ animationDelay: "0.2s" }}
              ></div>
            </div>
            <span className="text-sm">AI is thinking...</span>
          </motion.div>
        )}

        {/* Property Suggestions */}
        {chatState.suggestedProperties.length > 0 && (
          <PropertySuggestions
            properties={chatState.suggestedProperties}
            onPropertySelect={handlePropertySelect}
          />
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Chat Input */}
      <ChatInput
        onSendMessage={handleSendMessage}
        isLoading={chatState.isLoading}
        followUpQuestions={chatState.followUpQuestions}
        onQuestionClick={handleQuestionClick}
      />

      {/* Footer Actions */}
      <div className="p-2 md:p-3 bg-gray-100 dark:bg-gray-700 rounded-b-2xl flex items-center justify-between text-xs text-gray-500 dark:text-gray-400 flex-shrink-0">
        <button
          onClick={clearChat}
          className="hover:text-gray-700 dark:hover:text-gray-200 transition-colors px-2 py-1 rounded hover:bg-gray-200 dark:hover:bg-gray-600"
        >
          مسح المحادثة
        </button>
        <div className="flex items-center space-x-2 space-x-reverse">
          <span className="hidden sm:inline">Powered by OpenAI</span>
          <span className="sm:hidden">OpenAI</span>
          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
        </div>
      </div>
    </motion.div>
  );
}
