import { collection, getDocs, doc, query, orderBy, limit, setDoc, deleteDoc, getDocsFromServer } from "firebase/firestore";
import { ref, uploadBytes, getDownloadURL } from "firebase/storage";
import { db, storage } from "@/lib/firebase";
import { Property } from "@/components/Property";
import { v4 as uuidv4 } from "uuid";

// Get all properties
export const getAllProperties = async (): Promise<Property[]> => {
  try {
    const propertiesRef = collection(db, "properties");
    const q = query(propertiesRef, orderBy("nid", "desc"));
    const querySnapshot = await getDocs(q);

    const properties: Property[] = [];
    querySnapshot.forEach((doc) => {
      const data = doc.data();
      properties.push({
        nid: data.nid || 0,
        id: data.id || "",
        title: data.title || "",
        location: data.location || "",
        country: data.country || "",
        city: data.city || "",
        region: data.region || "",
        price: data.price || 0,
        type: data.type || "",
        bedrooms: data.bedrooms || 0,
        bathrooms: data.bathrooms || 0,
        area: data.area || 0,
        description: data.description || "",
        parking: data.parking || "",
        images: data.images || [],
        featured: data.featured || false,
        date: data.date || "",
        latitude: data.latitude || 0,
        longitude: data.longitude || 0,
      });
    });

    return properties;
  } catch (error) {
    console.error("Error fetching properties:", error);
    return [];
  }
};

// Get property by nid
export const getPropertyByNid = async (nid: number): Promise<Property | null> => {
  try {
    const propertiesRef = collection(db, "properties");
    const q = query(propertiesRef);
    const querySnapshot = await getDocs(q);

    for (const doc of querySnapshot.docs) {
      const data = doc.data();
      if (data.nid === nid) {
        return {
          nid: data.nid || 0,
          id: data.id || "",
          title: data.title || "",
          location: data.location || "",
          country: data.country || "",
          city: data.city || "",
          region: data.region || "",
          price: data.price || 0,
          type: data.type || "",
          bedrooms: data.bedrooms || 0,
          bathrooms: data.bathrooms || 0,
          area: data.area || 0,
          description: data.description || "",
          parking: data.parking || "",
          images: data.images || [],
          featured: data.featured || false,
          date: data.date || "",
          latitude: data.latitude || 0,
          longitude: data.longitude || 0,
        };
      }
    }

    return null;
  } catch (error) {
    console.error("Error fetching property:", error);
    return null;
  }
};

// Get featured properties
export const getFeaturedProperties = async (): Promise<Property[]> => {
  try {
    const propertiesRef = collection(db, "properties");
    const q = query(propertiesRef, orderBy("nid", "desc"));
    const querySnapshot = await getDocs(q);

    const properties: Property[] = [];
    querySnapshot.forEach((doc) => {
      const data = doc.data();
      if (data.featured) {
        properties.push({
          nid: data.nid || 0,
          id: data.id || "",
          title: data.title || "",
          location: data.location || "",
          country: data.country || "",
          city: data.city || "",
          region: data.region || "",
          price: data.price || 0,
          type: data.type || "",
          bedrooms: data.bedrooms || 0,
          bathrooms: data.bathrooms || 0,
          area: data.area || 0,
          description: data.description || "",
          parking: data.parking || "",
          images: data.images || [],
          featured: data.featured || false,
          date: data.date || "",
          latitude: data.latitude || 0,
          longitude: data.longitude || 0,
        });
      }
    });

    return properties;
  } catch (error) {
    console.error("Error fetching featured properties:", error);
    return [];
  }
};

// Get newest properties (limit to 4)
export const getNewestProperties = async (): Promise<Property[]> => {
  try {
    const propertiesRef = collection(db, "properties");
    const q = query(propertiesRef, orderBy("nid", "desc"), limit(4));
    const querySnapshot = await getDocs(q);

    const properties: Property[] = [];
    querySnapshot.forEach((doc) => {
      const data = doc.data();
      properties.push({
        nid: data.nid || 0,
        id: data.id || "",
        title: data.title || "",
        location: data.location || "",
        country: data.country || "",
        city: data.city || "",
        region: data.region || "",
        price: data.price || 0,
        type: data.type || "",
        bedrooms: data.bedrooms || 0,
        bathrooms: data.bathrooms || 0,
        area: data.area || 0,
        description: data.description || "",
        parking: data.parking || "",
        images: data.images || [],
        featured: data.featured || false,
        date: data.date || "",
        latitude: data.latitude || 0,
        longitude: data.longitude || 0,
      });
    });

    return properties;
  } catch (error) {
    console.error("Error fetching newest properties:", error);
    return [];
  }
};

// Upload image to Firebase Storage
export const uploadImage = async (file: File): Promise<string> => {
  try {
    const nowString = new Date().toISOString().split("T")[0];
    const yearStr = nowString.split("-")[0];
    const monthStr = nowString.split("-")[1];
    const storageRef = ref(storage, `properties/${yearStr}/${monthStr}/${uuidv4()}_${file.name}`);
    const snapshot = await uploadBytes(storageRef, file);
    const downloadURL = await getDownloadURL(snapshot.ref);
    return downloadURL;
  } catch (error) {
    console.error("Error uploading image:", error);
    throw new Error("Failed to upload image");
  }
};

// Get all property IDs (optimized for static generation)
export const getAllPropertyIds = async (): Promise<{ nid: number }[]> => {
  try {
    const propertiesRef = collection(db, "properties");
    const q = query(propertiesRef);
    const querySnapshot = await getDocs(q);

    const propertyIds: { nid: number }[] = [];
    querySnapshot.forEach((doc) => {
      const data = doc.data();
      if (data.nid) {
        propertyIds.push({ nid: data.nid });
      }
    });

    return propertyIds;
  } catch (error) {
    console.error("Error fetching property IDs:", error);
    return [];
  }
};

// Get the maximum nid value from all properties
// Uses server fetch to prevent race conditions from cached data
export const getMaxNid = async (): Promise<number> => {
  try {
    const propertiesRef = collection(db, "properties");
    const q = query(propertiesRef, orderBy("nid", "desc"), limit(1));
    // Force fetch from server, not cache, to prevent race conditions
    const querySnapshot = await getDocsFromServer(q);

    if (querySnapshot.empty) {
      return 0; // No properties exist yet, start from 1
    }

    const data = querySnapshot.docs[0].data();
    return data.nid || 0;
  } catch (error) {
    console.error("Error fetching max nid:", error);
    // Fallback: get all properties and find max (less efficient but more reliable)
    // Also force server fetch in fallback to ensure consistency
    try {
      const propertiesRef = collection(db, "properties");
      const q = query(propertiesRef);
      const querySnapshot = await getDocsFromServer(q);

      let maxNid = 0;
      querySnapshot.forEach((doc) => {
        const data = doc.data();
        if (data.nid && data.nid > maxNid) {
          maxNid = data.nid;
        }
      });

      return maxNid;
    } catch (fallbackError) {
      console.error("Error in fallback max nid fetch:", fallbackError);
      return 0;
    }
  }
};

// Update property
export const updateProperty = async (propertyId: string, propertyData: Partial<Property>): Promise<void> => {
  try {
    const propertiesRef = collection(db, "properties");
    const docRef = doc(propertiesRef, propertyId);

    // Filter out undefined values to prevent Firebase errors
    const cleanData = Object.fromEntries(Object.entries(propertyData).filter(([, value]) => value !== undefined));

    await setDoc(docRef, { ...cleanData, updatedAt: new Date().toISOString() }, { merge: true });
  } catch (error) {
    console.error("Error updating property:", error);
    throw new Error("Failed to update property");
  }
};

// Delete property
export const deleteProperty = async (propertyId: string): Promise<void> => {
  try {
    const propertiesRef = collection(db, "properties");
    const docRef = doc(propertiesRef, propertyId);
    await deleteDoc(docRef);
  } catch (error) {
    console.error("Error deleting property:", error);
    throw new Error("Failed to delete property");
  }
};

// Toggle property featured status
export const togglePropertyFeatured = async (propertyId: string, featured: boolean): Promise<void> => {
  try {
    await updateProperty(propertyId, { featured });
  } catch (error) {
    console.error("Error toggling property featured status:", error);
    throw new Error("Failed to toggle property status");
  }
};

// Create new property
export const createProperty = async (propertyData: Omit<Property, "nid" | "id" | "date">, images: File[], onProgress?: (progress: number) => void): Promise<string> => {
  const maxRetries = 3;
  let retryCount = 0;

  while (retryCount < maxRetries) {
    try {
      // Generate unique ID
      const propertyId = uuidv4();

      // Upload images with progress tracking
      const imageUrls: string[] = [];
      const totalImages = images.length;

      for (let i = 0; i < images.length; i++) {
        const image = images[i];
        const imageUrl = await uploadImage(image);
        imageUrls.push(imageUrl);

        // Update progress (50% for image uploads, 50% for saving to database)
        const imageProgress = ((i + 1) / totalImages) * 50;
        onProgress?.(imageProgress);
      }

      // Get the next nid by fetching the maximum value and incrementing
      // This is done at save time to prevent race conditions when multiple users
      // are adding properties simultaneously
      const maxNid = await getMaxNid();
      const nextNid = maxNid + 1;

      // Create property object
      const newProperty: Property = {
        nid: nextNid,
        id: propertyId,
        title: propertyData.title as string,
        location: propertyData.location as string,
        country: propertyData.country as string,
        city: propertyData.city as string,
        region: propertyData.region as string,
        price: propertyData.price as number,
        type: propertyData.type as string,
        bedrooms: propertyData.bedrooms as number,
        bathrooms: propertyData.bathrooms as number,
        area: propertyData.area as number,
        description: propertyData.description as string,
        parking: propertyData.parking as string,
        images: imageUrls,
        featured: propertyData.featured as boolean,
        date: new Date().toISOString().split("T")[0], // YYYY-MM-DD format
      };

      // Only add coordinates if they are defined and valid numbers
      if (propertyData.latitude !== undefined && !isNaN(propertyData.latitude)) {
        newProperty.latitude = propertyData.latitude;
      }
      if (propertyData.longitude !== undefined && !isNaN(propertyData.longitude)) {
        newProperty.longitude = propertyData.longitude;
      }

      // Save to Firestore using setDoc with the UUID as document ID
      const propertiesRef = collection(db, "properties");
      const docRef = doc(propertiesRef, propertyId);

      // Filter out undefined values to prevent Firebase errors
      const cleanProperty = Object.fromEntries(Object.entries(newProperty).filter(([, value]) => value !== undefined));

      await setDoc(docRef, cleanProperty);

      // Final progress update
      onProgress?.(100);

      return propertyId;
    } catch (error) {
      console.error(`Error creating property (attempt ${retryCount + 1}):`, error);
      retryCount++;

      if (retryCount >= maxRetries) {
        throw new Error("Failed to create property after multiple attempts");
      }

      // Wait a bit before retrying to reduce contention
      await new Promise((resolve) => setTimeout(resolve, 1000 * retryCount));
    }
  }

  throw new Error("Failed to create property after maximum retries");
};
