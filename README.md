# دار كاسا - موقع عقاري

موقع عقاري شامل مبني باستخدام Next.js و React.js مع واجهة عربية متجاوبة.

## المميزات

- 🏠 عرض العقارات المميزة
- 🔍 بحث متقدم مع فلاتر
- 📱 تصميم متجاوب للهواتف والأجهزة اللوحية
- 🎨 واجهة عربية جميلة وسهلة الاستخدام
- ⚡ أداء سريع مع Next.js
- 🎯 SEO محسن

## التقنيات المستخدمة

- **Next.js 14** - إطار عمل React
- **React 18** - مكتبة واجهة المستخدم
- **TypeScript** - لكتابة كود آمن ومنظم
- **Tailwind CSS** - لإطار العمل CSS
- **ESLint** - لفحص جودة الكود

## التثبيت والتشغيل

### المتطلبات الأساسية

- Node.js (الإصدار 18 أو أحدث)
- npm أو yarn

### خطوات التثبيت

1. استنسخ المشروع:

```bash
git clone <repository-url>
cd cursor-darcasa
```

2. ثبت التبعيات:

```bash
npm install
```

3. شغل المشروع في وضع التطوير:

```bash
npm run dev
```

4. افتح المتصفح على العنوان:

```
http://localhost:3000
```

### أوامر مفيدة

```bash
# تشغيل في وضع التطوير
npm run dev

# بناء المشروع للإنتاج
npm run build

# تشغيل النسخة المبنية
npm start

# فحص الكود
npm run lint
```

## هيكل المشروع

```
src/
├── app/
│   ├── layout.tsx          # التخطيط الرئيسي
│   ├── page.tsx            # الصفحة الرئيسية
│   └── globals.css         # الأنماط العامة
├── components/
│   ├── Header.tsx          # رأس الصفحة
│   ├── Hero.tsx            # القسم الرئيسي
│   ├── Features.tsx        # المميزات
│   ├── PropertyList.tsx    # قائمة العقارات
│   └── Footer.tsx          # تذييل الصفحة
```

## المميزات الرئيسية

### 1. البحث المتقدم

- بحث حسب نوع العقار (فيلا، شقة، مكتب، أرض)
- بحث حسب الموقع
- فلاتر متقدمة للأسعار والمساحة

### 2. عرض العقارات

- بطاقات جذابة للعقارات
- معلومات مفصلة (الغرف، الحمامات، المساحة)
- صور عالية الجودة
- علامات للعقارات المميزة

### 3. التصميم المتجاوب

- متوافق مع جميع أحجام الشاشات
- واجهة سهلة الاستخدام على الهواتف
- تصميم عصري وجذاب

### 4. الأداء المحسن

- تحميل سريع للصفحات
- صور محسنة
- كود نظيف ومنظم

## المساهمة

نرحب بمساهماتكم! يرجى اتباع الخطوات التالية:

1. Fork المشروع
2. إنشاء فرع جديد للميزة
3. إجراء التغييرات
4. إرسال Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT.

## الدعم

للاستفسارات والدعم، يرجى التواصل معنا:

- البريد الإلكتروني: <EMAIL>
- الهاتف: +966 11 123 4567

---

تم تطوير هذا المشروع بواسطة فريق دار كاسا 🏠
