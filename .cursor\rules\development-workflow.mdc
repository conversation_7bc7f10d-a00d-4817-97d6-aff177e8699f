# Development Workflow

## Project Setup

- Node.js and npm required
- Install dependencies: `npm install`
- Development server: `npm run dev`
- Build for production: `npm run build`
- Deploy to Firebase: `npm run deploy`

## Code Quality

- ESLint configuration in [eslint.config.mjs](mdc:eslint.config.mjs)
- Run linting: `npm run lint`
- Use TypeScript strict mode
- Follow consistent code formatting
- Use meaningful commit messages

## File Organization

- Keep components in `src/components/`
- Services in `src/services/`
- Utilities in `src/utils/`
- Context providers in `src/contexts/`
- Pages follow Next.js App Router structure

## Component Development

- Create reusable components
- Implement proper TypeScript interfaces
- Add JSDoc comments for complex functions
- Test components in isolation
- Use Storybook for component documentation (if needed)

## State Management

- Use React Context for global state
- Keep local state in components
- Implement proper loading states
- Handle error states gracefully
- Use optimistic updates where appropriate

## Testing Strategy

- Unit tests for utilities and services
- Component testing for UI components
- Integration tests for user flows
- E2E tests for critical paths
- Test Firebase operations with mocks

## Performance Optimization

- Use Next.js Image component for images
- Implement proper caching strategies
- Optimize bundle size
- Use dynamic imports for code splitting
- Monitor Core Web Vitals

## Deployment

- Firebase Hosting configuration in [firebase.json](mdc:firebase.json)
- Environment variables for different stages
- Proper build optimization
- CDN configuration for static assets
- Monitoring and error tracking setup
  description:
  globs:
  alwaysApply: false

---
