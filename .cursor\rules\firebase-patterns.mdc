# Firebase Integration Patterns

## Firebase Configuration

- Use the centralized Firebase config from [src/lib/firebase.ts](mdc:src/lib/firebase.ts)
- Import `db`, `auth`, and `storage` from the firebase config file
- Never hardcode Firebase credentials in components

## Authentication Patterns

- Use Firebase Auth for user management
- Implement auth state in [src/contexts/AuthContext.tsx](mdc:src/contexts/AuthContext.tsx)
- Handle auth errors gracefully with Arabic error messages
- Use email/password and Google sign-in methods
- Protect routes that require authentication

## Firestore Database Patterns

- Use the Property type from [src/components/Property.ts](mdc:src/components/Property.ts) for data consistency
- Implement CRUD operations in [src/services/propertyService.ts](mdc:src/services/propertyService.ts)
- Use proper Firestore security rules
- Implement real-time listeners for property updates
- Use batch operations for multiple document updates

## Storage Patterns

- Store property images in Firebase Storage
- Use UUID for unique file names
- Implement image compression before upload
- Handle upload progress and errors
- Clean up unused images when properties are deleted

## Error Handling

- Wrap Firebase operations in try-catch blocks
- Provide user-friendly error messages in Arabic
- Log errors for debugging purposes
- Implement retry logic for network failures

## Security Best Practices

- Validate data on both client and server side
- Use Firestore security rules to protect data
- Implement proper user authorization checks
- Sanitize user inputs before storing in database
  description:
  globs:
  alwaysApply: false

---
