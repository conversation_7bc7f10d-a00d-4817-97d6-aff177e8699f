import { getAllPropertyIds } from "@/services/propertyService";
import PropertyDetailsClient from "@/components/PropertyDetailsClient";

// Dynamic static params - fetches all property IDs at build time
export async function generateStaticParams() {
  try {
    console.log("🔍 Fetching property IDs for static generation...");
    const propertyIds = await getAllPropertyIds();

    const params = propertyIds.map((property) => ({
      id: property.nid.toString(),
    }));

    console.log(`✅ Generated static params for ${params.length} properties`);
    return params;
  } catch (error) {
    console.error("❌ Error generating static params:", error);
    // Fallback to empty array if Firebase is not available during build
    return [];
  }
}

export default function PropertyDetailsPage() {
  return <PropertyDetailsClient />;
}
