"use client";

import { useTheme } from "@/contexts/ThemeContext";
import { getStoredTheme, getThemeFromCookie, getSystemTheme, isSystemTheme } from "@/utils/themeUtils";

export default function ThemeInfo() {
  const { theme, isSystemTheme: contextIsSystemTheme } = useTheme();

  const getThemeInfo = () => {
    const storedTheme = getStoredTheme();
    const cookieTheme = getThemeFromCookie();
    const systemTheme = getSystemTheme();
    const isSystem = isSystemTheme();

    return {
      currentTheme: theme,
      storedTheme,
      cookieTheme,
      systemTheme,
      isSystem,
      contextIsSystemTheme,
    };
  };

  const themeInfo = getThemeInfo();

  return (
    <div className="fixed bottom-4 left-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4 shadow-lg max-w-sm z-50">
      <h3 className="text-sm font-semibold text-gray-900 dark:text-white mb-2">معلومات السمة</h3>
      <div className="space-y-1 text-xs text-gray-600 dark:text-gray-300">
        <div>
          السمة الحالية: <span className="font-medium text-green-600 dark:text-green-400">{themeInfo.currentTheme}</span>
        </div>
        <div>
          localStorage: <span className="font-medium">{themeInfo.storedTheme || "غير محفوظ"}</span>
        </div>
        <div>
          Cookie: <span className="font-medium">{themeInfo.cookieTheme || "غير محفوظ"}</span>
        </div>
        <div>
          النظام: <span className="font-medium">{themeInfo.systemTheme}</span>
        </div>
        <div>
          يتبع النظام: <span className="font-medium">{themeInfo.isSystem ? "نعم" : "لا"}</span>
        </div>
      </div>
      <div className="mt-2 text-xs text-gray-500 dark:text-gray-400">يتم حفظ تفضيلك تلقائياً</div>
    </div>
  );
}
