
"use client";

import { PropertyMatch } from '@/types/chat';
import { motion } from 'framer-motion';
import Link from 'next/link';
import { formatPrice, formatArea } from '@/utils/formatters';
import { getPropertyTypeText } from '../PropertyType';

interface PropertySuggestionsProps {
  properties: PropertyMatch[];
  onPropertySelect?: (property: PropertyMatch) => void;
}

export default function PropertySuggestions({ properties, onPropertySelect }: PropertySuggestionsProps) {
  if (properties.length === 0) return null;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4 }}
      className="mt-4 space-y-3"
    >
      <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
        🏠 Properties I found for you:
      </div>
      
      <div className="space-y-3 max-h-80 overflow-y-auto">
        {properties.map((propertyMatch, index) => (
          <motion.div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4 hover:shadow-md transition-shadow cursor-pointer">
            {/* Property Image */}
            <div className="relative mb-3">
              <img
                src={propertyMatch.property.images[0]}
                alt={propertyMatch.property.title}
                className="w-full h-32 object-cover rounded-lg"
              />
              <div className="absolute top-2 left-2 bg-green-600 text-white px-2 py-1 rounded-full text-xs font-medium">
                {Math.round(propertyMatch.score)}% Match
              </div>
            </div>

            {/* Property Details */}
            <div className="space-y-2">
              <h4 className="font-semibold text-gray-900 dark:text-white text-sm line-clamp-2">
                {propertyMatch.property.title}
              </h4>
              
              <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
                {propertyMatch.property.city}
              </div>

              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600 dark:text-gray-400">
                  {getPropertyTypeText(propertyMatch.property.type)}
                </span>
                <span className="font-semibold text-green-600 dark:text-green-400">
                  {formatPrice(propertyMatch.property.price)}
                </span>
              </div>

              <div className="flex items-center space-x-4 space-x-reverse text-xs text-gray-500 dark:text-gray-400">
                <span className="flex items-center">
                  <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
                  </svg>
                  {propertyMatch.property.bedrooms}
                </span>
                <span className="flex items-center">
                  <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 14v3m4-3v3m4-3v3M3 21h18M3 10h18M3 7l9-4 9 4M4 10h16v11H4V10z" />
                  </svg>
                  {propertyMatch.property.bathrooms}
                </span>
                <span className="flex items-center">
                  <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4" />
                  </svg>
                  {formatArea(propertyMatch.property.area)}
                </span>
              </div>

              {/* AI Explanation */}
              <div className="mt-2 p-2 bg-blue-50 dark:bg-blue-900/20 rounded text-xs text-blue-800 dark:text-blue-200">
                <div className="font-medium mb-1">🤖 Why this matches:</div>
                <div>{propertyMatch.aiExplanation}</div>
              </div>

              {/* Action Buttons */}
              <div className="flex space-x-2 space-x-reverse mt-3">
                <Link
                  href={`/property/${propertyMatch.property.nid}`}
                  className="flex-1 bg-green-600 hover:bg-green-700 text-white text-xs py-2 px-3 rounded text-center transition-colors"
                >
                  View Details
                </Link>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    // Add to favorites or save for later
                  }}
                  className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded text-xs text-gray-600 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                >
                  💾 Save
                </button>
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      <div className="text-xs text-gray-500 dark:text-gray-400 text-center pt-2">
        Click on any property to view full details
      </div>
    </motion.div>
  );
}
