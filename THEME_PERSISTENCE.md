# Theme Persistence System - دار كاسا

## Overview

The Dar Casa platform implements a robust theme persistence system that saves user theme preferences across sessions using multiple storage methods for maximum reliability.

## Features

### 🎯 **Multi-Storage Persistence**

- **localStorage** (Primary): Fast, persistent storage
- **Cookies** (Fallback): Works when localStorage is unavailable
- **System Preference Detection**: Automatically detects OS theme preference

### 🔄 **Automatic Theme Detection**

- Detects user's system theme preference (`prefers-color-scheme`)
- Automatically switches when system theme changes
- Respects user's explicit theme choice

### 🛡️ **Error Handling**

- Graceful fallbacks when storage is unavailable
- Console warnings for debugging
- No crashes if localStorage/cookies are disabled

### 📱 **Mobile Optimization**

- Updates `meta[name="theme-color"]` for mobile browsers
- Proper theme color in browser UI

## Implementation Details

### Storage Priority Order

1. **localStorage** - Primary storage method
2. **Cookies** - Fallback storage method
3. **System Preference** - Default when no saved preference

### Theme Context (`src/contexts/ThemeContext.tsx`)

```typescript
interface ThemeContextType {
  theme: Theme; // Current theme
  toggleTheme: () => void; // Toggle between themes
  setTheme: (theme: Theme) => void; // Set specific theme
  isSystemTheme: boolean; // Whether following system preference
}
```

### Utility Functions (`src/utils/themeUtils.ts`)

#### Core Functions

- `getStoredTheme()` - Get theme from localStorage
- `setStoredTheme(theme)` - Save theme to localStorage
- `getThemeFromCookie()` - Get theme from cookies
- `setThemeCookie(theme)` - Save theme to cookies
- `getSystemTheme()` - Detect system theme preference
- `applyTheme(theme)` - Apply theme to document
- `saveTheme(theme)` - Save to all storage methods
- `isSystemTheme()` - Check if using system preference

#### Helper Functions

- `getBestTheme()` - Get theme from best available source
- Error handling for all storage operations

## Usage Examples

### Basic Theme Toggle

```typescript
import { useTheme } from "@/contexts/ThemeContext";

function MyComponent() {
  const { theme, toggleTheme } = useTheme();

  return <button onClick={toggleTheme}>Current: {theme}</button>;
}
```

### Set Specific Theme

```typescript
import { useTheme } from "@/contexts/ThemeContext";

function MyComponent() {
  const { setTheme } = useTheme();

  return (
    <div>
      <button onClick={() => setTheme("light")}>Light</button>
      <button onClick={() => setTheme("dark")}>Dark</button>
    </div>
  );
}
```

### Check System Theme Status

```typescript
import { useTheme } from "@/contexts/ThemeContext";

function MyComponent() {
  const { isSystemTheme } = useTheme();

  return <div>{isSystemTheme ? "Following system preference" : "Using saved preference"}</div>;
}
```

## Storage Details

### localStorage

- **Key**: `"theme"`
- **Values**: `"light"` | `"dark"`
- **Persistence**: Until manually cleared
- **Scope**: Same origin

### Cookies

- **Name**: `"theme"`
- **Values**: `"light"` | `"dark"`
- **Expiry**: 1 year (`max-age=31536000`)
- **Attributes**: `SameSite=Lax`, `path=/`

### System Detection

- **Media Query**: `(prefers-color-scheme: dark)`
- **Event Listener**: Automatically updates when system theme changes
- **Fallback**: `"light"` if detection fails

## Browser Compatibility

### Supported Browsers

- ✅ Chrome/Chromium (all versions)
- ✅ Firefox (all versions)
- ✅ Safari (all versions)
- ✅ Edge (all versions)
- ✅ Mobile browsers (iOS Safari, Chrome Mobile)

### Feature Support

- ✅ localStorage
- ✅ Cookies
- ✅ `prefers-color-scheme` media query
- ✅ `matchMedia` API
- ✅ CSS custom properties
- ✅ Tailwind CSS dark mode

## Error Handling

### localStorage Errors

- Private browsing mode
- Storage quota exceeded
- Disabled by user

### Cookie Errors

- Cookies disabled
- Third-party cookie restrictions
- SameSite policy violations

### System Detection Errors

- Old browsers without `matchMedia`
- Privacy settings blocking detection

## Performance Considerations

### Optimizations

- Lazy loading of theme utilities
- Minimal re-renders with React context
- Efficient DOM updates
- Debounced system theme changes

### Memory Usage

- Small storage footprint (< 1KB)
- No memory leaks from event listeners
- Cleanup on component unmount

## Security Considerations

### Data Safety

- Only stores theme preference
- No sensitive user data
- SameSite cookie policy
- No cross-site scripting vulnerabilities

### Privacy

- Respects user's system preference
- No tracking or analytics
- Local storage only (no server communication)

## Testing

### Manual Testing

1. Toggle theme and refresh page
2. Clear localStorage and test cookie fallback
3. Change system theme and verify auto-switch
4. Test in private browsing mode
5. Test on mobile devices

### Automated Testing

```typescript
// Example test cases
describe("Theme Persistence", () => {
  it("should save theme to localStorage", () => {
    // Test implementation
  });

  it("should fallback to cookies when localStorage unavailable", () => {
    // Test implementation
  });

  it("should detect system theme preference", () => {
    // Test implementation
  });
});
```

## Troubleshooting

### Common Issues

#### Theme Not Persisting

- Check if localStorage is available
- Verify cookie settings
- Check browser privacy settings

#### System Theme Not Detected

- Verify `matchMedia` support
- Check system theme settings
- Test with different browsers

#### Hydration Mismatch

- Ensure proper mounting checks
- Verify SSR compatibility
- Check for client-side only code

### Debug Tools

- Use `ThemeInfo` component for debugging
- Check browser developer tools
- Monitor console warnings
- Test with different storage states

## Future Enhancements

### Potential Improvements

- Server-side theme detection
- Theme synchronization across devices
- Custom theme presets
- Animation preferences
- Accessibility theme options

### API Extensions

- Theme change events
- Theme validation
- Theme migration utilities
- Performance monitoring

---

**Last Updated**: December 2024  
**Version**: 1.0.0  
**Maintainer**: Dar Casa Development Team
