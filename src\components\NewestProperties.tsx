import { getNewestProperties } from "@/services/propertyService";
import PropertyCard from "./PropertyCard";

export default async function NewestProperties() {
  // Fetch newest properties from Firebase
  const newest = await getNewestProperties();

  return (
    <section className="py-16 bg-white dark:bg-gray-800">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">أحدث العقارات المضافة</h2>
          <p className="text-lg text-gray-600 dark:text-gray-300">تعرف على أحدث العقارات التي تم إضافتها مؤخرًا إلى منصتنا</p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {newest.map((property, index) => (
            <PropertyCard
              key={property.nid}
              property={property}
              variant="compact"
              showNewBadge={true}
              showTypeBadge={false}
              showFeaturedBadge={false}
              showPropertyDetails={false}
              index={index}
              animate={false}
            />
          ))}
        </div>
      </div>
    </section>
  );
}
