/* Chatbot-specific styles */

/* Hide scrollbars but keep functionality */
.scrollbar-hide {
  -ms-overflow-style: none;  /* Internet Explorer 10+ */
  scrollbar-width: none;  /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;  /* Safari and Chrome */
}

/* Prevent background scrolling when chatbot is open */
.chatbot-open {
  overflow: hidden;
}

/* Ensure chatbot is above everything */
.chatbot-widget {
  z-index: 9999;
}

/* Prevent text selection on buttons */
.chatbot-button {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
