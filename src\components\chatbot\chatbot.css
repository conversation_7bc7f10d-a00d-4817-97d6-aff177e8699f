/* Chatbot-specific styles */

/* Hide scrollbars but keep functionality */
.scrollbar-hide {
  -ms-overflow-style: none; /* Internet Explorer 10+ */
  scrollbar-width: none; /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none; /* Safari and Chrome */
}

/* Prevent background scrolling when chatbot is open */
.chatbot-open {
  overflow: hidden;
}

/* Ensure chatbot is above everything */
.chatbot-widget {
  z-index: 9999;
}

/* Prevent text selection on buttons */
.chatbot-button {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Mobile-specific optimizations */
@media (max-width: 640px) {
  .chatbot-widget {
    /* Full screen on mobile */
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    width: 100% !important;
    height: 100% !important;
    max-width: none !important;
    max-height: none !important;
    margin: 0 !important;
    border-radius: 0 !important;
  }

  /* Adjust header for mobile */
  .chatbot-widget .rounded-t-2xl {
    border-radius: 0 !important;
  }

  /* Adjust footer for mobile */
  .chatbot-widget .rounded-b-2xl {
    border-radius: 0 !important;
  }
}

/* Touch-friendly interactions */
@media (hover: none) and (pointer: coarse) {
  .chatbot-widget button {
    min-height: 44px;
    min-width: 44px;
  }

  .chatbot-widget .text-xs {
    font-size: 0.875rem;
  }
}
