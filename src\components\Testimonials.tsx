"use client";

import { motion } from "framer-motion";

export default function Testimonials() {
  const testimonials = [
    {
      name: "أحمد العتيبي",
      text: "خدمة ممتازة وسرعة في التواصل. وجدت العقار المناسب لعائلتي بسهولة!",
      city: "نحف",
      image: "https://randomuser.me/api/portraits/men/32.jpg",
    },
    {
      name: "سارة المالكي",
      text: "موقع رائع وسهل الاستخدام. فريق الدعم متعاون جدًا.",
      city: "الناصرة",
      image: "https://randomuser.me/api/portraits/women/44.jpg",
    },
    {
      name: "محمد الزهراني",
      text: "تنوع كبير في العقارات وأسعار مناسبة. أنصح الجميع باستخدام دار كاسا.",
      city: "يافا",
      image: "https://randomuser.me/api/portraits/men/65.jpg",
    },
    {
      name: "نورة السبيعي",
      text: "تجربة رائعة من البداية للنهاية. شكراً لكم!",
      city: "الطيرة",
      image: "https://randomuser.me/api/portraits/women/68.jpg",
    },
  ];

  return (
    <motion.section
      className="py-16 bg-white dark:bg-gray-900 transition-colors duration-200"
      id="testimonials"
      initial={{ opacity: 0, y: 40 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true, amount: 0.3 }}
      transition={{ duration: 0.8, ease: "easeOut" }}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">آراء العملاء</h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">ماذا يقول عملاؤنا عن تجربتهم مع دار كاسا</p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {testimonials.map((t, idx) => (
            <motion.div
              key={idx}
              className="card p-6 flex flex-col items-center text-center"
              initial={{ opacity: 0, y: 40 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true, amount: 0.3 }}
              transition={{ duration: 0.6, delay: idx * 0.15, ease: "easeOut" }}
            >
              <div className="w-16 h-16 rounded-full bg-green-100 dark:bg-green-900/20 flex items-center justify-center mb-4 overflow-hidden">
                <img src={t.image} alt={t.name} className="w-16 h-16 object-cover rounded-full" />
              </div>
              <p className="text-gray-700 dark:text-gray-300 mb-3">&quot;{t.text}&quot;</p>
              <div className="text-green-600 dark:text-green-400 font-semibold">{t.name}</div>
              <div className="text-gray-400 dark:text-gray-500 text-sm">{t.city}</div>
            </motion.div>
          ))}
        </div>
      </div>
    </motion.section>
  );
}
