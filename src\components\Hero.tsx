"use client";

import { motion } from "framer-motion";

export default function Hero() {
  const scrollToFilter = () => {
    const filterSection = document.getElementById("filter-section");
    if (filterSection) {
      filterSection.scrollIntoView({ behavior: "smooth" });
    }
  };

  return (
    <motion.section
      id="home"
      className="relative text-white mx-4 my-4 rounded-3xl overflow-hidden min-h-[400px] flex items-center justify-center"
      style={{
        // backgroundImage:
        //   "url('https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2075&q=80')",
        // backgroundSize: "cover",
        // backgroundPosition: "center",
        // backgroundRepeat: "no-repeat",
        boxShadow: "0 20px 40px rgba(0, 0, 0, 0.1)",
      }}
      initial={{ opacity: 0, y: 40 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true, amount: 0.3 }}
      transition={{ duration: 0.8, ease: "easeOut" }}
    >
      {/* Background matching header color */}
      <div
        className="absolute inset-0 rounded-3xl"
        style={{
          background: "#1A341B",
        }}
      ></div>

      {/* Subtle overlay for depth */}
      <div className="absolute inset-0 bg-gradient-to-br from-black/10 via-transparent to-black/15 rounded-3xl"></div>

      <div className="relative max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-0 text-center">
        {/* Company Logo */}
        <motion.div className="mb-6" initial={{ opacity: 0, y: 20 }} whileInView={{ opacity: 1, y: 0 }} transition={{ delay: 0.2, duration: 0.8 }}>
          <img src="/site-top-logo.png" alt="DAR.CASA" className="h-32 w-auto mx-auto drop-shadow-lg" />
        </motion.div>

        {/* Arabic Subtitle */}
        <motion.h2
          className="text-2xl md:text-3xl font-medium mb-12 text-white drop-shadow-lg"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4, duration: 0.8 }}
        >
          أفضل طريقة للبحث عن المنازل
        </motion.h2>

        {/* Search Bar */}
        <motion.div className="max-w-2xl mx-auto" initial={{ opacity: 0, y: 20 }} whileInView={{ opacity: 1, y: 0 }} transition={{ delay: 0.6, duration: 0.8 }}>
          <div className="relative">
            <div className="bg-white rounded-full shadow-xl overflow-hidden">
              {/* Search Button */}
              <button
                onClick={scrollToFilter}
                className="w-full bg-white hover:bg-gray-50 text-green-600 px-8 py-4 transition-colors duration-200 font-medium flex items-center justify-center space-x-2"
              >
                <span className="text-2xl font-bold ml-4">إبدأ بحثا جديدا</span>
                <svg className="w-7 h-7" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </button>
            </div>
          </div>
        </motion.div>
      </div>
    </motion.section>
  );
}
