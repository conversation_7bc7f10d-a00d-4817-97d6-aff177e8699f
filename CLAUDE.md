# CLAUDE.md

This file provides guidance to Claude <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

**Essential commands for development:**
- `npm run dev` - Start development server (localhost:3000)
- `npm run build` - Build the application for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint to check code quality
- `npm run export` - Build and export static files
- `npm run deploy` - Build, export, and deploy to Firebase Hosting

## Project Architecture

This is a **Next.js 15** real estate website (دار كاسا) with the following key architectural patterns:

### Tech Stack
- **Next.js 15** with App Router and static export configuration
- **React 19** with TypeScript
- **Firebase** (Firestore, Storage, Auth) for backend services
- **Tailwind CSS** for styling with RTL Arabic support
- **Tajawal Google Font** for Arabic typography

### Core Architecture Patterns

**Static Export Configuration:**
- Configured for static site generation (`output: "export"`)
- Optimized for Firebase Hosting deployment
- Images are unoptimized for static export compatibility

**Firebase Integration:**
- Uses Firebase v9+ modular SDK
- Property data stored in Firestore with collection "properties"
- Images uploaded to Firebase Storage with UUID-based naming
- Authentication system in place (though minimal implementation)

**Property Data Model:**
```typescript
interface Property {
  nid: number;        // Numeric ID for URLs
  id: string;         // UUID for Firestore document ID
  title: string;
  location: string;
  country: string;
  city: string;
  region: string;
  price: number;
  type: string;
  bedrooms: number;
  bathrooms: number;
  area: number;
  description: string;
  parking: string;
  images: string[];
  featured: boolean;
  date: string;
}
```

**Service Layer Pattern:**
- `propertyService.ts` handles all property CRUD operations
- `authService.ts` handles authentication (basic implementation)
- All Firebase operations are centralized in service files

**Component Structure:**
- Page components in `src/app/` (App Router structure)
- Reusable UI components in `src/components/`
- Context providers for global state (AuthContext)
- Utility functions in `src/utils/`

**Routing Strategy:**
- Dynamic routes use `[id]` folders for property details and editing
- Property URLs use numeric IDs (`/property/1`) but internally use UUID for Firestore
- Dashboard and auth pages are separate route groups

**Styling Approach:**
- RTL (right-to-left) layout configured in root layout
- Arabic language support with Tajawal font
- Tailwind CSS with custom configurations
- Responsive design patterns throughout

### Key Implementation Notes

**Property Management:**
- Properties use dual ID system: `nid` for user-facing URLs, `id` (UUID) for database
- Image uploads handled with progress tracking
- Featured properties system for homepage highlighting
- Search and filtering capabilities (though basic implementation)

**Authentication Flow:**
- AuthContext provides global auth state
- Protected routes for dashboard and property management
- Firebase Auth integration with minimal UI

**Performance Considerations:**
- Static export optimized for Firebase Hosting
- Image optimization disabled for static compatibility
- Firebase package imports optimized in Next.js config
- Console logs removed in production builds

## Firebase Configuration

The project is configured for Firebase Hosting with:
- Public directory: `out/` (Next.js export output)
- SPA rewrites configured for client-side routing
- Firebase project: "dar-casa"