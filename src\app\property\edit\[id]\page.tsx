import Link from "next/link";
import { getPropertyByNid, getAllPropertyIds } from "@/services/propertyService";
import EditPropertyForm from "@/components/EditPropertyForm";
import Header from "@/components/Header";
import Footer from "@/components/Footer";

// Generate static params for static export
export async function generateStaticParams() {
  try {
    console.log("🔍 Fetching property IDs for edit page static generation...");
    const propertyIds = await getAllPropertyIds();

    const params = propertyIds.map((property) => ({
      id: property.nid.toString(),
    }));

    console.log(`✅ Generated static params for ${params.length} edit pages`);
    return params;
  } catch (error) {
    console.error("❌ Error generating static params for edit pages:", error);
    return [];
  }
}

export default async function EditPropertyPage({ params }: { params: Promise<{ id: string }> }) {
  const { id } = await params;
  const propertyId = parseInt(id);

  if (isNaN(propertyId)) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <Header />
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">خطأ</h1>
            <p className="text-gray-600 dark:text-gray-400 mb-8">معرف العقار غير صحيح</p>
            <Link href="/dashboard" className="btn-primary">
              العودة للوحة التحكم
            </Link>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  const property = await getPropertyByNid(propertyId);

  if (!property) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <Header />
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">خطأ</h1>
            <p className="text-gray-600 dark:text-gray-400 mb-8">العقار غير موجود</p>
            <Link href="/dashboard" className="btn-primary">
              العودة للوحة التحكم
            </Link>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <Header />

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-8">
          <div className="flex items-center justify-between mb-8">
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">تعديل العقار</h1>
            <Link href="/dashboard" className="text-green-600 dark:text-green-400 hover:text-green-700 dark:hover:text-green-300">
              العودة للوحة التحكم
            </Link>
          </div>

          <EditPropertyForm property={property} />
        </div>
      </div>

      <Footer />
    </div>
  );
}
