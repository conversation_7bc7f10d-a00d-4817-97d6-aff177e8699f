"use client";

import { useEffect, useState } from "react";
import dynamic from "next/dynamic";
import type { Icon } from "leaflet";

// Dynamically import the map components to avoid SSR issues
const MapContainer = dynamic(() => import("react-leaflet").then((mod) => mod.MapContainer), {
  ssr: false,
  loading: () => <div className="h-64 bg-gray-200 animate-pulse rounded-lg flex items-center justify-center">جاري تحميل الخريطة...</div>,
});

const TileLayer = dynamic(() => import("react-leaflet").then((mod) => mod.TileLayer), {
  ssr: false,
});

const Marker = dynamic(() => import("react-leaflet").then((mod) => mod.Marker), {
  ssr: false,
});

const Popup = dynamic(() => import("react-leaflet").then((mod) => mod.Popup), {
  ssr: false,
});

interface PropertyLocationMapProps {
  latitude?: number;
  longitude?: number;
  propertyTitle?: string;
  height?: string;
}

export default function PropertyLocationMap({ latitude, longitude, propertyTitle = "موقع العقار", height = "h-64" }: PropertyLocationMapProps) {
  const [isClient, setIsClient] = useState(false);
  const [redMarkerIcon, setRedMarkerIcon] = useState<Icon | null>(null);

  useEffect(() => {
    setIsClient(true);
    // Create a custom red marker icon (only on client side)
    if (isClient) {
      import("leaflet").then((L) => {
        const icon = new L.default.Icon({
          iconUrl:
            "data:image/svg+xml;base64," +
            btoa(`
      <svg width="24" height="36" viewBox="0 0 24 36" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M12 0C5.373 0 0 5.373 0 12c0 10.5 12 24 12 24s12-13.5 12-24c0-6.627-5.373-12-12-12z" fill="#dc2626"/>
        <circle cx="12" cy="12" r="6" fill="white"/>
        <circle cx="12" cy="12" r="3" fill="#dc2626"/>
      </svg>
    `),
          iconSize: [24, 36],
          iconAnchor: [12, 36],
          popupAnchor: [0, -36],
        });
        setRedMarkerIcon(icon);
      });
    }
  }, [isClient]);

  // If no coordinates provided, show a placeholder
  if (!latitude || !longitude) {
    return (
      <div className={`${height} bg-gray-100 rounded-lg flex items-center justify-center border border-gray-300`}>
        <div className="text-center">
          <svg className="w-12 h-12 mx-auto mb-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
          <p className="text-gray-500">لم يتم تحديد الموقع على الخريطة</p>
        </div>
      </div>
    );
  }

  if (!isClient) {
    return <div className={`${height} bg-gray-200 animate-pulse rounded-lg flex items-center justify-center`}>جاري تحميل الخريطة...</div>;
  }

  return (
    <div className={`${height} rounded-lg overflow-hidden border border-gray-300`}>
      <MapContainer center={[latitude, longitude]} zoom={15} style={{ height: "100%", width: "100%" }} className="z-0">
        <TileLayer attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors' url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png" />

        {redMarkerIcon ? (
          <Marker position={[latitude, longitude]} icon={redMarkerIcon}>
            <Popup>
              <div className="text-center">
                <h3 className="font-semibold text-gray-900">{propertyTitle}</h3>
                <p className="text-sm text-gray-600">
                  {latitude.toFixed(6)}, {longitude.toFixed(6)}
                </p>
              </div>
            </Popup>
          </Marker>
        ) : (
          <Marker position={[latitude, longitude]}>
            <Popup>
              <div className="text-center">
                <h3 className="font-semibold text-gray-900">{propertyTitle}</h3>
                <p className="text-sm text-gray-600">
                  {latitude.toFixed(6)}, {longitude.toFixed(6)}
                </p>
              </div>
            </Popup>
          </Marker>
        )}
      </MapContainer>
    </div>
  );
}
