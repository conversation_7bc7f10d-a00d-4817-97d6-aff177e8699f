"use client";

import { useState, useRef } from "react";
import { createProperty } from "@/services/propertyService";
import LocationMap from "./LocationMap";
import { PropertyType, PropertyTypes } from "./PropertyType";

interface AddPropertyFormProps {
  onClose: () => void;
  onSuccess: () => void;
}

interface ImageFile {
  file: File;
  preview: string;
  id: string;
}

export default function AddPropertyForm({ onClose, onSuccess }: AddPropertyFormProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [success, setSuccess] = useState("");
  const [selectedImages, setSelectedImages] = useState<ImageFile[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const [formData, setFormData] = useState({
    title: "",
    location: "",
    country: "فلسطين",
    city: "",
    region: "",
    price: "",
    type: "apartment",
    bedrooms: "",
    bathrooms: "",
    area: "",
    description: "",
    parking: "موقف سيارات خاص",
    featured: false,
    latitude: undefined as number | undefined,
    longitude: undefined as number | undefined,
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? (e.target as HTMLInputElement).checked : value,
    }));
  };

  const handleLocationSelect = (lat: number, lng: number) => {
    setFormData((prev) => ({
      ...prev,
      latitude: lat,
      longitude: lng,
    }));
  };

  const handleImageSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);

    const newImages: ImageFile[] = files.map((file) => ({
      file,
      preview: URL.createObjectURL(file),
      id: Math.random().toString(36).substr(2, 9),
    }));

    setSelectedImages((prev) => [...prev, ...newImages]);
  };

  const removeImage = (id: string) => {
    setSelectedImages((prev) => {
      const imageToRemove = prev.find((img) => img.id === id);
      if (imageToRemove) {
        URL.revokeObjectURL(imageToRemove.preview);
      }
      return prev.filter((img) => img.id !== id);
    });
  };

  const moveImage = (fromIndex: number, toIndex: number) => {
    setSelectedImages((prev) => {
      const newImages = [...prev];
      const [movedImage] = newImages.splice(fromIndex, 1);
      newImages.splice(toIndex, 0, movedImage);
      return newImages;
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (selectedImages.length === 0) {
      alert("يرجى اختيار صورة واحدة على الأقل");
      return;
    }

    setIsLoading(true);
    setProgress(0);

    try {
      // The nid will be automatically assigned by the createProperty function
      // which fetches the current maximum nid at save time and increments it by 1
      // This prevents race conditions when multiple users are adding properties simultaneously
      const propertyData: {
        title: string;
        location: string;
        country: string;
        city: string;
        region: string;
        price: number;
        type: string;
        bedrooms: number;
        bathrooms: number;
        area: number;
        description: string;
        parking: string;
        images: string[];
        featured: boolean;
        latitude?: number;
        longitude?: number;
      } = {
        title: formData.title,
        location: formData.location,
        country: formData.country,
        city: formData.city,
        region: formData.region,
        price: parseFloat(formData.price),
        type: formData.type,
        bedrooms: parseInt(formData.bedrooms),
        bathrooms: parseInt(formData.bathrooms),
        area: parseFloat(formData.area),
        description: formData.description,
        parking: formData.parking,
        images: [],
        featured: formData.featured,
      };

      // Only add coordinates if they are defined and valid numbers
      if (formData.latitude !== undefined && formData.longitude !== undefined && !isNaN(formData.latitude) && !isNaN(formData.longitude)) {
        propertyData.latitude = formData.latitude;
        propertyData.longitude = formData.longitude;
      }

      // Extract files in the current order
      const files = selectedImages.map((img) => img.file);

      await createProperty(propertyData, files, (progressValue) => {
        setProgress(progressValue);
      });

      setSuccess("تم إضافة العقار بنجاح!");

      // Don't close immediately, let user see the success message
      // User can close manually or we can auto-close after delay
      setTimeout(() => {
        onSuccess();
        onClose();
      }, 4000);
    } catch (error) {
      console.error("Error creating property:", error);
      alert("حدث خطأ أثناء إضافة العقار. يرجى المحاولة مرة أخرى.");
    } finally {
      setIsLoading(false);
      setProgress(0);
    }
  };

  return (
    <>
      {/* Success Toast Notification */}
      {success && (
        <div className="fixed top-4 left-1/2 transform -translate-x-1/2 z-[60] bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg">
          <div className="flex items-center">
            <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            {success}
          </div>
        </div>
      )}

      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white">إضافة عقار جديد</h2>
              <button onClick={onClose} className="text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300 text-2xl font-bold" disabled={isLoading}>
                ×
              </button>
            </div>
          </div>

          <form onSubmit={handleSubmit} className="p-6 space-y-6">
            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">عنوان العقار *</label>
                <input
                  type="text"
                  name="title"
                  value={formData.title}
                  onChange={handleInputChange}
                  required
                  disabled={isLoading}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 dark:focus:ring-green-400 focus:border-transparent disabled:opacity-50 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                  placeholder="مثال: فيلا فاخرة في عكا"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الموقع *</label>
                <input
                  type="text"
                  name="location"
                  value={formData.location}
                  onChange={handleInputChange}
                  required
                  disabled={isLoading}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 dark:focus:ring-green-400 focus:border-transparent disabled:opacity-50 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                  placeholder="مثال: عكا، حي المينا"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">المدينة *</label>
                <input
                  type="text"
                  name="city"
                  value={formData.city}
                  onChange={handleInputChange}
                  required
                  disabled={isLoading}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 dark:focus:ring-green-400 focus:border-transparent disabled:opacity-50 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                  placeholder="مثال: عكا"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">المنطقة *</label>
                <input
                  type="text"
                  name="region"
                  value={formData.region}
                  onChange={handleInputChange}
                  required
                  disabled={isLoading}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 dark:focus:ring-green-400 focus:border-transparent disabled:opacity-50 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                  placeholder="مثال: حيفا"
                />
              </div>
            </div>

            {/* Property Details */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">نوع العقار *</label>
                <select
                  name="type"
                  value={formData.type}
                  onChange={handleInputChange}
                  required
                  disabled={isLoading}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 dark:focus:ring-green-400 focus:border-transparent disabled:opacity-50 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                >
                  {PropertyTypes.map((type: PropertyType) => (
                    <option key={type.code} value={type.code}>
                      {type.name_ar}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">السعر (دولار) *</label>
                <input
                  type="number"
                  name="price"
                  value={formData.price}
                  onChange={handleInputChange}
                  required
                  min="0"
                  disabled={isLoading}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 dark:focus:ring-green-400 focus:border-transparent disabled:opacity-50 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                  placeholder="مثال: 2500000"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">المساحة (متر مربع) *</label>
                <input
                  type="number"
                  name="area"
                  value={formData.area}
                  onChange={handleInputChange}
                  required
                  min="0"
                  disabled={isLoading}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 dark:focus:ring-green-400 focus:border-transparent disabled:opacity-50 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                  placeholder="مثال: 450"
                />
              </div>
            </div>

            {/* Rooms and Parking */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">غرف النوم</label>
                <input
                  type="number"
                  name="bedrooms"
                  value={formData.bedrooms}
                  onChange={handleInputChange}
                  min="0"
                  disabled={isLoading}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 dark:focus:ring-green-400 focus:border-transparent disabled:opacity-50 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                  placeholder="مثال: 3"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الحمامات</label>
                <input
                  type="number"
                  name="bathrooms"
                  value={formData.bathrooms}
                  onChange={handleInputChange}
                  min="0"
                  disabled={isLoading}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 dark:focus:ring-green-400 focus:border-transparent disabled:opacity-50 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                  placeholder="مثال: 2"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">موقف السيارات</label>
                <select
                  name="parking"
                  value={formData.parking}
                  onChange={handleInputChange}
                  disabled={isLoading}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 dark:focus:ring-green-400 focus:border-transparent disabled:opacity-50 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                >
                  <option value="موقف سيارات خاص">موقف سيارات خاص</option>
                  <option value="موقف سيارات مشترك">موقف سيارات مشترك</option>
                  <option value="غير محدد">غير محدد</option>
                </select>
              </div>
            </div>

            {/* Description */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الوصف *</label>
              <textarea
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                required
                rows={4}
                disabled={isLoading}
                className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 dark:focus:ring-green-400 focus:border-transparent disabled:opacity-50 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                placeholder="وصف مفصل للعقار..."
              />
            </div>

            {/* Location Map */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">موقع العقار على الخريطة</label>
              <LocationMap latitude={formData.latitude} longitude={formData.longitude} onLocationSelect={handleLocationSelect} height="h-80" />
              {formData.latitude && formData.longitude && (
                <div className="mt-2 text-sm text-green-600 dark:text-green-400">
                  تم تحديد الموقع: {formData.latitude.toFixed(6)}, {formData.longitude.toFixed(6)}
                </div>
              )}
            </div>

            {/* Featured Property */}
            <div className="flex items-center">
              <input
                type="checkbox"
                name="featured"
                checked={formData.featured}
                onChange={handleInputChange}
                disabled={isLoading}
                className="h-4 w-4 text-green-600 focus:ring-green-500 dark:focus:ring-green-400 border-gray-300 dark:border-gray-600 rounded disabled:opacity-50"
              />
              <label className="mr-2 text-sm font-medium text-gray-700 dark:text-gray-300">عقار مميز</label>
            </div>

            {/* Image Upload */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">صور العقار *</label>
              <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6 text-center">
                <input ref={fileInputRef} type="file" multiple accept="image/*" onChange={handleImageSelect} disabled={isLoading} className="hidden" />
                <button type="button" onClick={() => fileInputRef.current?.click()} disabled={isLoading} className="btn-primary mb-4 disabled:opacity-50">
                  اختيار الصور
                </button>
                <p className="text-sm text-gray-500 dark:text-gray-400">يمكنك اختيار عدة صور. اسحب وأفلت لإعادة ترتيب الصور.</p>
              </div>

              {/* Image Previews with Drag & Drop */}
              {selectedImages.length > 0 && (
                <div className="mt-4">
                  <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الصور المختارة (اسحب لإعادة الترتيب):</h4>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    {selectedImages.map((image, index) => (
                      <div
                        key={image.id}
                        className="relative group cursor-move"
                        draggable
                        onDragStart={(e) => {
                          e.dataTransfer.setData("text/plain", index.toString());
                        }}
                        onDragOver={(e) => e.preventDefault()}
                        onDrop={(e) => {
                          e.preventDefault();
                          const fromIndex = parseInt(e.dataTransfer.getData("text/plain"));
                          moveImage(fromIndex, index);
                        }}
                      >
                        <img src={image.preview} alt={`Preview ${index + 1}`} className="w-full h-32 object-cover rounded-lg" />
                        <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200 rounded-lg flex items-center justify-center">
                          <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 text-white text-sm font-medium">اسحب لإعادة الترتيب</div>
                        </div>
                        <div className="absolute top-2 right-2 flex space-x-1 space-x-reverse">
                          <button
                            type="button"
                            onClick={() => removeImage(image.id)}
                            disabled={isLoading}
                            className="bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm hover:bg-red-600 disabled:opacity-50"
                          >
                            ×
                          </button>
                          <div className="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-medium">{index + 1}</div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Progress Bar */}
            {isLoading && (
              <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-4">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-blue-700 dark:text-blue-300">جاري حفظ العقار...</span>
                  <span className="text-sm text-blue-600 dark:text-blue-400">{Math.round(progress)}%</span>
                </div>
                <div className="w-full bg-blue-200 dark:bg-blue-800 rounded-full h-2">
                  <div className="bg-blue-600 dark:bg-blue-400 h-2 rounded-full transition-all duration-300" style={{ width: `${progress}%` }}></div>
                </div>
              </div>
            )}

            {/* Submit Button */}
            <div className="flex justify-end space-x-4 space-x-reverse pt-6 border-t border-gray-200 dark:border-gray-700">
              <button
                type="button"
                onClick={onClose}
                disabled={isLoading}
                className="px-6 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors disabled:opacity-50"
              >
                إلغاء
              </button>
              <button type="submit" disabled={isLoading} className="btn-primary px-8 py-3 disabled:opacity-50 disabled:cursor-not-allowed">
                {isLoading ? "جاري الإضافة..." : "إضافة العقار"}
              </button>
            </div>
          </form>
        </div>
      </div>
    </>
  );
}
