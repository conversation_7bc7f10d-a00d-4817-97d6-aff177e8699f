"use client";

import { useEffect, useState } from "react";
import ChatbotToggle from "./ChatbotToggle";
import { Property } from "@/components/Property";

interface ChatbotProviderProps {
  properties: Property[];
}

export default function ChatbotProvider({ properties }: ChatbotProviderProps) {
  const [mounted, setMounted] = useState(false);
  const [hasApiKey, setHasApiKey] = useState(false);

  useEffect(() => {
    setMounted(true);
    // Check if OpenAI API key is available
    const apiKey = process.env.NEXT_PUBLIC_OPENAI_API_KEY;
    console.log(apiKey);
    setHasApiKey(!!apiKey && apiKey !== "************************************************************************************************************");
  }, []);

  if (!mounted) {
    return null;
  }

  // Don't render chatbot if API key is missing
  if (!hasApiKey) {
    return (
      <div className="fixed bottom-6 right-6 z-50">
        <div className="bg-yellow-500 text-white p-4 rounded-full shadow-lg">
          <div className="text-xs text-center">
            <div>🔑 API Key Required</div>
            <div>Check .env.local</div>
          </div>
        </div>
      </div>
    );
  }

  return <ChatbotToggle availableProperties={properties} />;
}
