import Header from "@/components/Header";
import Hero from "@/components/Hero";
import FilterSection from "@/components/FilterSection";
import Features from "@/components/Features";
import About from "@/components/About";
import AnimatedNewestProperties from "@/components/AnimatedNewestProperties";
import PropertyList from "@/components/PropertyList";
import Testimonials from "@/components/Testimonials";
import Footer from "@/components/Footer";
import ChatbotProvider from "@/components/chatbot/ChatbotProvider";
import { getAllProperties, getNewestProperties } from "@/services/propertyService";

// For debugging theme persistence, you can add:
// import ThemeInfo from "@/components/ThemeInfo";
// Then add <ThemeInfo /> before the closing </main> tag

export default async function Home() {
  // Fetch properties from Firebase
  const properties = await getAllProperties();
  const newestProperties = await getNewestProperties();

  return (
    <main className="min-h-screen">
      <Header />
      <Hero />
      <FilterSection allProperties={properties} />
      <AnimatedNewestProperties properties={newestProperties} />
      <PropertyList properties={properties} />
      <Features />
      <Testimonials />
      <About />
      <Footer />
      {/* AI Chatbot */}
      <ChatbotProvider properties={properties} />
      {/* Uncomment the line below to see theme persistence debug info */}
      {/* <ThemeInfo /> */}
    </main>
  );
}
