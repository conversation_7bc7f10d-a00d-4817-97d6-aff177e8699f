"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { useAuth } from "@/contexts/AuthContext";
import { updateProperty, uploadImage } from "@/services/propertyService";
import { Property } from "./Property";
import LocationMap from "./LocationMap";
import { PropertyType, PropertyTypes } from "./PropertyType";

interface EditPropertyFormProps {
  property: Property;
}

interface ImageFile {
  file: File;
  preview: string;
  id: string;
}

export default function EditPropertyForm({ property }: EditPropertyFormProps) {
  const { user, loading } = useAuth();
  const router = useRouter();
  const [isSaving, setIsSaving] = useState(false);
  const [progress, setProgress] = useState(0);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");
  const [newImages, setNewImages] = useState<ImageFile[]>([]);
  const [existingImages, setExistingImages] = useState<string[]>(property.images || []);

  const [formData, setFormData] = useState({
    title: property.title || "",
    location: property.location || "",
    country: property.country || "",
    city: property.city || "",
    region: property.region || "",
    price: property.price?.toString() || "",
    type: property.type || "",
    bedrooms: property.bedrooms?.toString() || "",
    bathrooms: property.bathrooms?.toString() || "",
    area: property.area?.toString() || "",
    description: property.description || "",
    parking: property.parking || "",
    featured: property.featured || false,
    latitude: property.latitude || 0,
    longitude: property.longitude || 0,
  });

  // Redirect if not authenticated
  if (!loading && !user) {
    router.push("/signin");
    return null;
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? (e.target as HTMLInputElement).checked : value,
    }));
  };

  const handleLocationSelect = (lat: number, lng: number) => {
    setFormData((prev) => ({
      ...prev,
      latitude: lat,
      longitude: lng,
    }));
  };

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const files = Array.from(e.target.files);
      const newImageFiles: ImageFile[] = files.map((file) => ({
        file,
        preview: URL.createObjectURL(file),
        id: Math.random().toString(36).substr(2, 9),
      }));
      setNewImages((prev) => [...prev, ...newImageFiles]);
    }
  };

  const removeNewImage = (id: string) => {
    setNewImages((prev) => {
      const imageToRemove = prev.find((img) => img.id === id);
      if (imageToRemove) {
        URL.revokeObjectURL(imageToRemove.preview);
      }
      return prev.filter((img) => img.id !== id);
    });
  };

  const removeExistingImage = (index: number) => {
    setExistingImages((prev) => prev.filter((_, i) => i !== index));
  };

  const moveExistingImage = (fromIndex: number, toIndex: number) => {
    setExistingImages((prev) => {
      const newImages = [...prev];
      const [movedImage] = newImages.splice(fromIndex, 1);
      newImages.splice(toIndex, 0, movedImage);
      return newImages;
    });
  };

  const moveNewImage = (fromIndex: number, toIndex: number) => {
    setNewImages((prev) => {
      const newImages = [...prev];
      const [movedImage] = newImages.splice(fromIndex, 1);
      newImages.splice(toIndex, 0, movedImage);
      return newImages;
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");
    setSuccess("");
    setIsSaving(true);
    setProgress(0);

    try {
      // Upload new images with progress tracking
      const newImageUrls: string[] = [];
      const totalNewImages = newImages.length;

      if (totalNewImages > 0) {
        for (let i = 0; i < newImages.length; i++) {
          const imageFile = newImages[i];
          const imageUrl = await uploadImage(imageFile.file);
          newImageUrls.push(imageUrl);

          // Update progress (50% for image uploads, 50% for saving to database)
          const imageProgress = ((i + 1) / totalNewImages) * 50;
          setProgress(imageProgress);
        }
      } else {
        // If no new images, set progress to 50% immediately
        setProgress(50);
      }

      // Combine existing and new images
      const allImages = [...existingImages, ...newImageUrls];

      // Prepare update data
      const updateData: {
        title: string;
        location: string;
        country: string;
        city: string;
        region: string;
        price: number;
        type: string;
        bedrooms: number;
        bathrooms: number;
        area: number;
        description: string;
        parking: string;
        featured: boolean;
        images: string[];
        latitude?: number;
        longitude?: number;
      } = {
        title: formData.title,
        location: formData.location,
        country: formData.country,
        city: formData.city,
        region: formData.region,
        price: parseFloat(formData.price) || 0,
        type: formData.type,
        bedrooms: parseInt(formData.bedrooms) || 0,
        bathrooms: parseInt(formData.bathrooms) || 0,
        area: parseFloat(formData.area) || 0,
        description: formData.description,
        parking: formData.parking,
        featured: formData.featured,
        images: allImages,
      };

      // Only add coordinates if they are defined
      if (formData.latitude !== undefined && formData.longitude !== undefined) {
        updateData.latitude = formData.latitude;
        updateData.longitude = formData.longitude;
      }

      // Update progress to 75% before database save
      setProgress(75);

      // Update property
      await updateProperty(property.id, updateData);

      // Final progress update
      setProgress(100);

      setSuccess("تم تحديث العقار بنجاح!");

      // Auto-dismiss success message after 3 seconds
      setTimeout(() => {
        setSuccess("");
      }, 3000);

      // Redirect to dashboard after a short delay
      setTimeout(() => {
        router.push("/dashboard");
      }, 4000);
    } catch (error) {
      console.error("Error updating property:", error);
      setError("حدث خطأ أثناء تحديث العقار");

      // Auto-dismiss error message after 5 seconds
      setTimeout(() => {
        setError("");
      }, 5000);
    } finally {
      setIsSaving(false);
      setProgress(0);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
      </div>
    );
  }

  return (
    <>
      {/* Success Toast Notification */}
      {success && (
        <div className="fixed top-4 left-1/2 transform -translate-x-1/2 z-[60] bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg">
          <div className="flex items-center">
            <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            {success}
          </div>
        </div>
      )}

      {/* Error Toast Notification */}
      {error && (
        <div className="fixed top-4 left-1/2 transform -translate-x-1/2 z-[60] bg-red-500 text-white px-6 py-3 rounded-lg shadow-lg">
          <div className="flex items-center">
            <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
            {error}
          </div>
        </div>
      )}

      <div>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">عنوان العقار *</label>
              <input type="text" name="title" value={formData.title} onChange={handleInputChange} required className="input-field" placeholder="أدخل عنوان العقار" />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الموقع *</label>
              <input type="text" name="location" value={formData.location} onChange={handleInputChange} required className="input-field" placeholder="أدخل الموقع" />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">البلد</label>
              <input type="text" name="country" value={formData.country} onChange={handleInputChange} className="input-field" placeholder="أدخل البلد" />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">المدينة</label>
              <input type="text" name="city" value={formData.city} onChange={handleInputChange} className="input-field" placeholder="أدخل المدينة" />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">المنطقة</label>
              <input type="text" name="region" value={formData.region} onChange={handleInputChange} className="input-field" placeholder="أدخل المنطقة" />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">السعر *</label>
              <input type="number" name="price" value={formData.price} onChange={handleInputChange} required min="0" className="input-field" placeholder="أدخل السعر" />
            </div>
          </div>

          {/* Property Details */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">نوع العقار</label>
              <select name="type" value={formData.type} onChange={handleInputChange} className="input-field">
                <option value="">اختر النوع</option>
                {PropertyTypes.map((type: PropertyType) => (
                  <option key={type.code} value={type.code}>
                    {type.name_ar}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">غرف النوم</label>
              <input type="number" name="bedrooms" value={formData.bedrooms} onChange={handleInputChange} min="0" className="input-field" placeholder="عدد الغرف" />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الحمامات</label>
              <input type="number" name="bathrooms" value={formData.bathrooms} onChange={handleInputChange} min="0" className="input-field" placeholder="عدد الحمامات" />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">المساحة (م²)</label>
              <input type="number" name="area" value={formData.area} onChange={handleInputChange} min="0" className="input-field" placeholder="المساحة" />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">موقف السيارات</label>
            <input type="text" name="parking" value={formData.parking} onChange={handleInputChange} className="input-field" placeholder="معلومات موقف السيارات" />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الوصف</label>
            <textarea name="description" value={formData.description} onChange={handleInputChange} rows={4} className="input-field" placeholder="أدخل وصف العقار" />
          </div>

          {/* Location Map */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">موقع العقار على الخريطة</label>
            <LocationMap latitude={formData.latitude} longitude={formData.longitude} onLocationSelect={handleLocationSelect} height="h-80" />
            {formData.latitude && formData.longitude && (
              <div className="mt-2 text-sm text-green-600 dark:text-green-400">
                تم تحديد الموقع: {formData.latitude.toFixed(6)}, {formData.longitude.toFixed(6)}
              </div>
            )}
          </div>

          {/* Featured Status */}
          <div className="flex items-center">
            <input
              type="checkbox"
              name="featured"
              checked={formData.featured}
              onChange={handleInputChange}
              className="h-4 w-4 text-green-600 focus:ring-green-500 dark:focus:ring-green-400 border-gray-300 dark:border-gray-600 rounded"
            />
            <label className="mr-2 block text-sm text-gray-900 dark:text-gray-100">عرض العقار في الصفحة الرئيسية</label>
          </div>

          {/* Existing Images */}
          {existingImages.length > 0 && (
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الصور الحالية</label>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {existingImages.map((image, index) => (
                  <div
                    key={index}
                    className="relative group cursor-move"
                    draggable
                    onDragStart={(e) => {
                      e.dataTransfer.setData("text/plain", index.toString());
                    }}
                    onDragOver={(e) => e.preventDefault()}
                    onDrop={(e) => {
                      e.preventDefault();
                      const fromIndex = parseInt(e.dataTransfer.getData("text/plain"));
                      moveExistingImage(fromIndex, index);
                    }}
                  >
                    <img src={image} alt={`صورة ${index + 1}`} className="w-full h-32 object-cover rounded-lg" />
                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200 rounded-lg flex items-center justify-center">
                      <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 text-white text-sm font-medium">اسحب لإعادة الترتيب</div>
                    </div>
                    <div className="absolute top-2 right-2 flex space-x-1 space-x-reverse">
                      <button
                        type="button"
                        onClick={() => removeExistingImage(index)}
                        className="bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm hover:bg-red-600"
                      >
                        ×
                      </button>
                      <div className="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-medium">{index + 1}</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* New Images */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">إضافة صور جديدة</label>
            <input type="file" multiple accept="image/*" onChange={handleImageChange} className="input-field" />

            {newImages.length > 0 && (
              <div className="mt-4 grid grid-cols-2 md:grid-cols-4 gap-4">
                {newImages.map((imageFile, index) => (
                  <div
                    key={imageFile.id}
                    className="relative group cursor-move"
                    draggable
                    onDragStart={(e) => {
                      e.dataTransfer.setData("text/plain", index.toString());
                    }}
                    onDragOver={(e) => e.preventDefault()}
                    onDrop={(e) => {
                      e.preventDefault();
                      const fromIndex = parseInt(e.dataTransfer.getData("text/plain"));
                      moveNewImage(fromIndex, index);
                    }}
                  >
                    <img src={imageFile.preview} alt="صورة جديدة" className="w-full h-32 object-cover rounded-lg" />
                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200 rounded-lg flex items-center justify-center">
                      <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 text-white text-sm font-medium">اسحب لإعادة الترتيب</div>
                    </div>
                    <div className="absolute top-2 right-2 flex space-x-1 space-x-reverse">
                      <button
                        type="button"
                        onClick={() => removeNewImage(imageFile.id)}
                        className="bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm hover:bg-red-600"
                      >
                        ×
                      </button>
                      <div className="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-medium">{index + 1}</div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Progress Bar */}
          {isSaving && (
            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-4">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-blue-700 dark:text-blue-300">جاري حفظ التغييرات...</span>
                <span className="text-sm text-blue-600 dark:text-blue-400">{Math.round(progress)}%</span>
              </div>
              <div className="w-full bg-blue-200 dark:bg-blue-800 rounded-full h-2">
                <div className="bg-blue-600 dark:bg-blue-400 h-2 rounded-full transition-all duration-300" style={{ width: `${progress}%` }}></div>
              </div>
            </div>
          )}

          {/* Submit Buttons */}
          <div className="flex justify-end space-x-4 space-x-reverse pt-6">
            <Link
              href="/dashboard"
              className="px-6 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
            >
              إلغاء
            </Link>
            <button type="submit" disabled={isSaving} className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed">
              {isSaving ? (
                <div className="flex items-center">
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                  جاري الحفظ...
                </div>
              ) : (
                "حفظ التغييرات"
              )}
            </button>
          </div>
        </form>
      </div>
    </>
  );
}
