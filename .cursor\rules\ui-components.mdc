# UI Component Patterns

## Component Structure

- Use functional components with hooks
- Implement proper TypeScript interfaces for props
- Follow single responsibility principle
- Use composition over inheritance

## Styling Standards

- Use Tailwind CSS classes for styling
- Follow the theme configuration in [tailwind.config.ts](mdc:tailwind.config.ts)
- Support both light and dark themes
- Use RTL-aware classes for Arabic text
- Implement responsive design with mobile-first approach

## Component Categories

- **Layout Components**: Header, Footer, Layout wrappers
- **Property Components**: Property cards, property details, property forms
- **UI Components**: Buttons, forms, modals, navigation
- **Map Components**: Location maps using Leaflet
- **Animation Components**: Use Framer Motion for smooth animations

## Accessibility

- Use semantic HTML elements
- Implement proper ARIA labels
- Ensure keyboard navigation support
- Provide alt text for images
- Support screen readers

## Performance

- Use React.memo for expensive components
- Implement lazy loading for images
- Use proper key props for lists
- Avoid unnecessary re-renders
- Optimize bundle size with dynamic imports

## State Management

- Use React Context for global state (auth, theme)
- Use local state for component-specific data
- Implement proper loading and error states
- Use optimistic updates where appropriate

## Form Handling

- Use controlled components for forms
- Implement proper validation
- Show validation errors in Arabic
- Handle form submission states
- Use proper input types and labels
  description:
  globs:
  alwaysApply: false

---
