@tailwind base;
@tailwind components;
@tailwind utilities;

/* Leaflet CSS */
@import 'leaflet/dist/leaflet.css';

@layer base {
  html {
    direction: rtl;
    scroll-behavior: smooth;
  }
  
  body {
    font-family: var(--font-tajawal), '<PERSON><PERSON><PERSON>', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  }
}

@layer components {
  .btn-primary {
    @apply bg-white dark:bg-gray-800 text-green-700 dark:text-green-400 font-semibold py-2 px-4 rounded-lg transition-colors duration-200 shadow-md;
    border: 1px solid #22c55e; /* green-500 */
  }

  .btn-primary:hover, .btn-primary:focus {
    @apply bg-green-50 dark:bg-green-900/20 text-green-800 dark:text-green-300 shadow-lg;
    border-color: #16a34a; /* green-600 */
  }

  .btn-primary-red-shadow{
    box-shadow: -3px 3px 0 0 #dc2626;
  }

  .btn-primary-red-left{
    box-shadow: -5px 0 0 0 #dc2626;
  }

  .btn-secondary {
    @apply bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-800 dark:text-gray-200 font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }
  
  .card {
    @apply bg-white dark:bg-gray-800 rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300;
  }
  
  .input-field {
    @apply w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 dark:focus:ring-green-400 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100;
  }
}
