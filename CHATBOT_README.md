# AI Property Search Chatbot

## Overview

This AI-powered chatbot helps users find properties through natural language conversations. It integrates with OpenAI's GPT models to understand user intent and provide intelligent property recommendations.

## Features

### 🤖 AI-Powered Search

- Natural language property search
- Intent recognition and entity extraction
- Smart property matching with scoring
- Multi-language support (Arabic/English)

### 💬 Interactive Chat Interface

- Real-time chat with AI assistant
- Follow-up questions and suggestions
- Property recommendations with explanations
- Chat history and session management

### 🏠 Property Intelligence

- AI-generated property explanations
- Confidence scoring for matches
- Smart filtering and recommendations
- Context-aware responses

## Setup

### 1. Environment Variables

Create a `.env.local` file in your project root:

```bash
NEXT_PUBLIC_OPENAI_API_KEY=your_openai_api_key_here
```

### 2. Install Dependencies

```bash
npm install openai langchain @langchain/openai @langchain/community react-markdown react-syntax-highlighter lucide-react date-fns
```

### 3. OpenAI API Key

- Get your API key from [OpenAI Platform](https://platform.openai.com/)
- Add it to your `.env.local` file
- **Note**: For production, use a backend proxy to keep the API key secure

## Usage

### Basic Chat

Users can ask questions like:

- "I need a 3-bedroom apartment in Rome under €500,000"
- "Show me family homes near good schools"
- "What properties are available in Milan?"

### AI Responses

The chatbot will:

1. Understand user intent
2. Search available properties
3. Provide ranked recommendations
4. Explain why properties match
5. Ask follow-up questions

### Property Matching

Properties are scored based on:

- Location match (30 points)
- Price range (25 points)
- Property type (20 points)
- Bedrooms (15 points)
- Bathrooms (10 points)

## Components

### Core Components

- `ChatbotWidget.tsx` - Main chat interface
- `ChatMessage.tsx` - Individual message display
- `ChatInput.tsx` - User input with suggestions
- `PropertySuggestions.tsx` - AI-recommended properties
- `ChatbotToggle.tsx` - Floating action button

### Services

- `openaiService.ts` - OpenAI API integration
- `chatService.ts` - Chat state management

### Types

- `chat.ts` - TypeScript interfaces for chat system

## Architecture

```
User Input → ChatInput → ChatService → OpenAIService → OpenAI API
                ↓              ↓              ↓
            UI Update    State Update    AI Response
                ↓              ↓              ↓
            Property    Intent Parse    Property Match
         Suggestions    & Scoring      & Explanation
```

## Customization

### System Prompts

Modify the system prompt in `openaiService.ts` to:

- Change AI personality
- Add specific guidelines
- Include custom property knowledge

### Property Scoring

Adjust scoring weights in `findMatchingProperties()` to:

- Prioritize different criteria
- Add custom scoring logic
- Implement business rules

### UI Styling

Customize the chat interface by modifying:

- Color schemes in component classes
- Animation parameters in Framer Motion
- Layout and spacing in Tailwind CSS

## Security Considerations

### Development

- API key is exposed in client-side code
- Use for testing and development only

### Production

- Implement backend proxy for API calls
- Add rate limiting and user authentication
- Monitor API usage and costs

## Performance

### Optimization

- Chat history is stored in memory
- Property data is cached from main page
- API calls are debounced and optimized

### Monitoring

- Track API response times
- Monitor token usage
- Log user interactions for improvement

## Future Enhancements

### Planned Features

- Voice input/output
- Image analysis for properties
- Advanced filtering algorithms
- User preference learning
- Multi-session support

### Integration Opportunities

- CRM systems
- Lead generation
- Analytics and reporting
- Multi-language expansion

## Troubleshooting

### Common Issues

1. **API Key Error**: Check `.env.local` file
2. **Properties Not Loading**: Verify Firebase connection
3. **Chat Not Responding**: Check OpenAI API status
4. **Styling Issues**: Ensure Tailwind CSS is loaded

### Debug Mode

Enable console logging in `openaiService.ts` to debug:

- API requests and responses
- Property matching logic
- User intent extraction

## Support

For issues or questions:

1. Check the console for error messages
2. Verify environment variables
3. Test OpenAI API connectivity
4. Review component dependencies

## License

This chatbot implementation is part of the DarCasa project and follows the same licensing terms.
