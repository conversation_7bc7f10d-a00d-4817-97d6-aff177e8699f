# Demo Properties for Dar Casa

This directory contains demo properties data for your Dar Casa real estate website, featuring beautiful properties in Italian cities with Arabic text and Euro prices.

## Files

- **`demo-properties.json`** - Contains 12 demo properties with complete data
- **`push-demo-properties.js`** - Node.js script to push properties to Firebase
- **`DEMO_PROPERTIES_README.md`** - This instruction file

## Demo Properties Included

The demo properties cover various Italian cities and regions:

### Featured Properties (3)

1. **روما (Rome)** - Villa in historical center - €850,000
2. **ميلانو (Milan)** - Modern apartment in fashion district - €650,000
3. **البندقية (Venice)** - Historic palace on Grand Canal - €1,200,000

### Regular Properties (9)

4. **فلورنسا (Florence)** - Rural villa in Tuscany - €750,000
5. **نابولي (Naples)** - Classic apartment - €380,000
6. **تورينو (Turin)** - Luxury apartment - €520,000
7. **بولونيا (Bologna)** - Modern villa - €680,000
8. **جنوة (Genoa)** - Sea-view apartment - €450,000
9. **باليرمو (Palermo)** - Sicilian villa - €580,000
10. **فيرونا (Verona)** - Romantic apartment - €420,000
11. **سيينا (Siena)** - Tuscan villa - €890,000
12. **بيرغامو (Bergamo)** - Modern apartment - €380,000

## Property Features

Each property includes:

- ✅ Arabic titles and descriptions
- ✅ Euro prices (€380,000 - €1,200,000)
- ✅ Beautiful Unsplash images (3 per property)
- ✅ Accurate Italian city locations
- ✅ Realistic property specifications
- ✅ GPS coordinates for mapping
- ✅ Various property types (villas, apartments, palaces)
- ✅ Different bedroom/bathroom configurations
- ✅ Parking information
- ✅ Featured status for premium properties

## How to Use

### Option 1: Manual Import via Firebase Console

1. Go to your Firebase Console
2. Navigate to Firestore Database
3. Create a new collection called "properties"
4. Import the `demo-properties.json` file or copy individual properties

### Option 2: Automated Script (Recommended)

1. Install dependencies:

   ```bash
   npm install firebase
   ```

2. Update the Firebase configuration in `push-demo-properties.js`:

   ```javascript
   const firebaseConfig = {
     apiKey: "your-api-key",
     authDomain: "your-project.firebaseapp.com",
     projectId: "your-project-id",
     storageBucket: "your-project.appspot.com",
     messagingSenderId: "your-sender-id",
     appId: "your-app-id",
   };
   ```

3. Run the script:
   ```bash
   node push-demo-properties.js
   ```

## Image Sources

All images are from Unsplash and are free to use:

- High-quality real estate photography
- Optimized for web (800x600 with crop fit)
- Diverse architectural styles
- Professional appearance

## Data Structure

The properties follow your exact Property type structure:

- `nid`: Sequential numbering (1-12)
- `id`: Unique demo identifier
- `title`: Arabic property title
- `location`: Italian street address
- `country`: إيطاليا (Italy)
- `city`: Arabic city name
- `region`: Arabic region name
- `price`: Euro amount
- `type`: Property type in Arabic
- `bedrooms`: Number of bedrooms
- `bathrooms`: Number of bathrooms
- `area`: Square meters
- `description`: Detailed Arabic description
- `parking`: Parking information in Arabic
- `images`: Array of 3 Unsplash image URLs
- `featured`: Boolean for premium properties
- `date`: ISO date string
- `latitude/longitude`: GPS coordinates

## Notes

- The script automatically adds `createdAt` and `updatedAt` timestamps
- Properties are added with a 500ms delay to avoid overwhelming the database
- The `nid` field is removed during import as it will be auto-generated by your system
- All text content is in Arabic as requested
- Prices are in Euros as requested
- Images are high-quality and free from Unsplash

## Customization

You can easily modify the properties by:

- Editing the JSON file
- Adding more properties
- Changing image URLs
- Adjusting prices or descriptions
- Modifying property types or locations

After making changes, simply run the script again to update your database.
