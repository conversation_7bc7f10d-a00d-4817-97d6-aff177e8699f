"use client";

import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Property } from "./Property";
// import { formatPrice, formatArea } from "@/utils/formatters";
// import Link from "next/link";
import PropertyCard from "./PropertyCard";
import { PropertyType, PropertyTypes } from "./PropertyType";

interface FilterSectionProps {
  allProperties: Property[];
}

export default function FilterSection({ allProperties }: FilterSectionProps) {
  // const [searchType, setSearchType] = useState("buy");
  const [city, setCity] = useState("");
  const [propertyType, setPropertyType] = useState("all");
  const [areaFrom, setAreaFrom] = useState("");
  const [areaTo, setAreaTo] = useState("");
  const [priceFrom, setPriceFrom] = useState("");
  const [priceTo, setPriceTo] = useState("");

  const [filteredProperties, setFilteredProperties] = useState<Property[]>([]);
  const [isFiltered, setIsFiltered] = useState(false);
  const [isClearing, setIsClearing] = useState(false);

  // Get unique cities from properties
  const cities = Array.from(new Set(allProperties.map((p) => p.city).filter(Boolean))).sort();

  // Apply filters
  const applyFilters = () => {
    let filtered = [...allProperties];

    // Filter by city
    if (city) {
      filtered = filtered.filter((property) => property.city.toLowerCase().includes(city.toLowerCase()));
    }

    // Filter by property type
    if (propertyType !== "all") {
      filtered = filtered.filter((property) => property.type === propertyType);
    }

    // Filter by area range
    if (areaFrom) {
      filtered = filtered.filter((property) => property.area >= parseFloat(areaFrom));
    }
    if (areaTo) {
      filtered = filtered.filter((property) => property.area <= parseFloat(areaTo));
    }

    // Filter by price range
    if (priceFrom) {
      filtered = filtered.filter((property) => property.price >= parseFloat(priceFrom));
    }
    if (priceTo) {
      filtered = filtered.filter((property) => property.price <= parseFloat(priceTo));
    }

    setFilteredProperties(filtered);
    setIsFiltered(true);
  };

  // Clear all filters with smooth animation
  const clearFilters = () => {
    setIsClearing(true);

    // Wait for the fade-out animation to complete before clearing data
    setTimeout(() => {
      // setSearchType("buy");
      setCity("");
      setPropertyType("all");
      setAreaFrom("");
      setAreaTo("");
      setPriceFrom("");
      setPriceTo("");
      setFilteredProperties([]);
      setIsFiltered(false);
      setIsClearing(false);
    }, 300); // Match the animation duration
  };

  return (
    <motion.section
      id="filter-section"
      className="py-16 bg-white dark:bg-gray-900 transition-colors duration-200"
      initial={{ opacity: 0, y: 40 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true, amount: 0.3 }}
      transition={{ duration: 0.8, ease: "easeOut" }}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-8">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">ابحث عن عقارك المثالي</h2>
          <p className="text-xl text-gray-600 dark:text-gray-300">اكتشف أفضل العقارات في إيطاليا</p>
        </div>

        {/* Search Form */}
        <div className="max-w-6xl mx-auto">
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-2xl border border-gray-200 dark:border-gray-700">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
              {/* Search Type
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">نوع البحث</label>
                <select
                  value={searchType}
                  onChange={(e) => setSearchType(e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 dark:focus:ring-green-400 focus:border-transparent text-gray-800 dark:text-gray-100 bg-white dark:bg-gray-700"
                >
                  <option value="buy">شراء</option>
                  <option value="rent">إيجار</option>
                  <option value="sell">بيع</option>
                </select>
              </div> */}

              {/* City */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">المدينة</label>
                <select
                  value={city}
                  onChange={(e) => setCity(e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 dark:focus:ring-green-400 focus:border-transparent text-gray-800 dark:text-gray-100 bg-white dark:bg-gray-700"
                >
                  <option value="">جميع المدن</option>
                  {cities.map((cityName) => (
                    <option key={cityName} value={cityName}>
                      {cityName}
                    </option>
                  ))}
                </select>
              </div>

              {/* Property Type */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">نوع العقار</label>
                <select
                  value={propertyType}
                  onChange={(e) => setPropertyType(e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 dark:focus:ring-green-400 focus:border-transparent text-gray-800 dark:text-gray-100 bg-white dark:bg-gray-700"
                >
                  <option value="all">جميع الأنواع</option>
                  {PropertyTypes.map((type: PropertyType) => (
                    <option key={type.code} value={type.code}>
                      {type.name_ar}
                    </option>
                  ))}
                </select>
              </div>

              {/* Area Range */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">المساحة (م²)</label>
                <div className="grid grid-cols-2 gap-2">
                  <input
                    type="number"
                    placeholder="من"
                    value={areaFrom}
                    onChange={(e) => setAreaFrom(e.target.value)}
                    className="w-full px-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 dark:focus:ring-green-400 focus:border-transparent text-gray-800 dark:text-gray-100 bg-white dark:bg-gray-700"
                  />
                  <input
                    type="number"
                    placeholder="إلى"
                    value={areaTo}
                    onChange={(e) => setAreaTo(e.target.value)}
                    className="w-full px-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 dark:focus:ring-green-400 focus:border-transparent text-gray-800 dark:text-gray-100 bg-white dark:bg-gray-700"
                  />
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
              {/* Price Range */}
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">السعر (€)</label>
                <div className="grid grid-cols-2 gap-2">
                  <input
                    type="number"
                    placeholder="من"
                    value={priceFrom}
                    onChange={(e) => setPriceFrom(e.target.value)}
                    className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 dark:focus:ring-green-400 focus:border-transparent text-gray-800 dark:text-gray-100 bg-white dark:bg-gray-700"
                  />
                  <input
                    type="number"
                    placeholder="إلى"
                    value={priceTo}
                    onChange={(e) => setPriceTo(e.target.value)}
                    className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 dark:focus:ring-green-400 focus:border-transparent text-gray-800 dark:text-gray-100 bg-white dark:bg-gray-700"
                  />
                </div>
              </div>

              {/* Search Button */}
              <div className="flex items-end">
                <button onClick={applyFilters} className="w-full bg-green-600 hover:bg-green-700 text-white font-semibold py-3 px-6 rounded-lg transition duration-300">
                  بحث
                </button>
              </div>

              {/* Clear Filters Button */}
              <div className="flex items-end">
                <button onClick={clearFilters} className="w-full bg-gray-500 hover:bg-gray-600 text-white font-semibold py-3 px-6 rounded-lg transition duration-300">
                  مسح الفلاتر
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Filtered Results with AnimatePresence */}
        <AnimatePresence mode="wait">
          {isFiltered && !isClearing && (
            <motion.div
              className="mt-12"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3, ease: "easeInOut" }}
            >
              <div className="text-center mb-8">
                <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">نتائج البحث ({filteredProperties.length} عقار)</h3>
                <p className="text-gray-600 dark:text-gray-300">
                  {filteredProperties.length > 0 ? `تم العثور على ${filteredProperties.length} عقار يطابق معايير البحث` : "لم يتم العثور على عقارات تطابق معايير البحث"}
                </p>
              </div>

              {filteredProperties.length > 0 && (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                  {filteredProperties.map((property, index) => (
                    <PropertyCard key={property.nid} property={property} variant="default" showTypeBadge={true} showFeaturedBadge={true} showPropertyDetails={true} index={index} />
                  ))}
                </div>
              )}
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </motion.section>
  );
}
