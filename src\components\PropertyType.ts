export type PropertyType = {
    code: string;
    name_en: string;
    name_ar: string;
    plural_name_en: string;
    plural_name_ar: string;
    orderKey: number;
  }
  
  export const PropertyTypes: PropertyType[] = [
    {
      code: "villa",
      name_en: "Villa",
      name_ar: "فيلا",
      plural_name_en: "Villas",
      plural_name_ar: "فيلات",
      orderKey: 1,
    },
    {
      code: "apartment",
      name_en: "Apartment",
      name_ar: "شقة",
      plural_name_en: "Apartments",
      plural_name_ar: "شقق",
      orderKey: 2,
    },
    {
      code: "office",
      name_en: "Office",
      name_ar: "مكتب",
      plural_name_en: "Offices",
      plural_name_ar: "مكاتب",
      orderKey: 3,
    },
    {
      code: "land",
      name_en: "Land",
      name_ar: "أرض",
      plural_name_en: "Lands",
      plural_name_ar: "أراضي",
      orderKey: 4,
    },
    {
      code: "shop",
      name_en: "Shop",
      name_ar: "محل",
      plural_name_en: "Shops",
      plural_name_ar: "محلات",
      orderKey: 5,
    },
    
    {
      code: "penthouse",
      name_en: "Penthouse",
      name_ar: "بنتهاوس",
      plural_name_en: "Penthouses",
      plural_name_ar: "بنتهاوسات",
      orderKey: 6,
    },
    
    {
      code: "palace",
      name_en: "Palace",
      name_ar: "قصر",
      plural_name_en: "Palaces",
      plural_name_ar: "قصور",
      orderKey: 7,
    },
    {
        code: "house",  
        name_en: "House",
        name_ar: "منزل",
        plural_name_en: "Houses",
        plural_name_ar: "منازل",
        orderKey: 8,
      },
    
  ];
  
  export const getPropertyTypeText = (typeCode: string) => {
    const type = PropertyTypes.find((t: PropertyType) => t.code === typeCode);
    return type ? type.name_ar : typeCode;
  };