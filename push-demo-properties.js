import { initializeApp } from "firebase/app";
import { getFirestore, collection, addDoc } from "firebase/firestore";
import fs from "fs";

// Your Firebase configuration
const firebaseConfig = {
  // Add your Firebase config here
  // apiKey: "your-api-key",
  // authDomain: "your-project.firebaseapp.com",
  // projectId: "your-project-id",
  // storageBucket: "your-project.appspot.com",
  // messagingSenderId: "your-sender-id",
  // appId: "your-app-id"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

// Read demo properties from JSON file
const demoProperties = JSON.parse(fs.readFileSync("./demo-properties.json", "utf8"));

async function pushDemoProperties() {
  try {
    console.log("Starting to push demo properties to Firebase...");

    for (const property of demoProperties) {
      // Remove the nid and id fields as they will be auto-generated
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { nid, id, ...propertyData } = property;

      // Add the property to Firestore
      const docRef = await addDoc(collection(db, "properties"), {
        ...propertyData,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      });

      console.log(`✅ Added property: ${property.title} (ID: ${docRef.id})`);

      // Add a small delay to avoid overwhelming the database
      await new Promise((resolve) => setTimeout(resolve, 500));
    }

    console.log("🎉 All demo properties have been successfully pushed to Firebase!");
  } catch (error) {
    console.error("❌ Error pushing demo properties:", error);
  }
}

// Run the function
pushDemoProperties();
