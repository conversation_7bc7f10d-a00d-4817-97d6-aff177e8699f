"use client";

import React, { createContext, useContext, useEffect, useState } from "react";

type Theme = "light" | "dark";

interface ThemeContextType {
  theme: Theme;
  toggleTheme: () => void;
  setTheme: (theme: Theme) => void;
  isSystemTheme: boolean;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

// Helper function to safely access localStorage
const getStoredTheme = (): Theme | null => {
  try {
    if (typeof window !== "undefined") {
      return localStorage.getItem("theme") as Theme;
    }
  } catch (error) {
    console.warn("Failed to access localStorage:", error);
  }
  return null;
};

// Helper function to safely set localStorage
const setStoredTheme = (theme: Theme): void => {
  try {
    if (typeof window !== "undefined") {
      localStorage.setItem("theme", theme);
      // Also set a cookie as fallback
      document.cookie = `theme=${theme}; path=/; max-age=31536000; SameSite=Lax`;
    }
  } catch (error) {
    console.warn("Failed to save theme to localStorage:", error);
  }
};

// Helper function to get theme from cookie
const getThemeFromCookie = (): Theme | null => {
  try {
    if (typeof document !== "undefined") {
      const cookies = document.cookie.split(";");
      const themeCookie = cookies.find((cookie) => cookie.trim().startsWith("theme="));
      if (themeCookie) {
        const theme = themeCookie.split("=")[1] as Theme;
        return theme === "light" || theme === "dark" ? theme : null;
      }
    }
  } catch (error) {
    console.warn("Failed to read theme from cookie:", error);
  }
  return null;
};

// Helper function to detect system theme preference
const getSystemTheme = (): Theme => {
  try {
    if (typeof window !== "undefined" && window.matchMedia) {
      return window.matchMedia("(prefers-color-scheme: dark)").matches ? "dark" : "light";
    }
  } catch (error) {
    console.warn("Failed to detect system theme:", error);
  }
  return "light";
};

export function ThemeProvider({ children }: { children: React.ReactNode }) {
  const [theme, setThemeState] = useState<Theme>("light");
  const [mounted, setMounted] = useState(false);
  const [isSystemTheme, setIsSystemTheme] = useState(false);

  useEffect(() => {
    // Get theme from multiple sources in order of preference
    let savedTheme: Theme | null = null;

    // 1. Try localStorage first
    savedTheme = getStoredTheme();

    // 2. Try cookie as fallback
    if (!savedTheme) {
      savedTheme = getThemeFromCookie();
    }

    // 3. Use system preference if no saved theme
    if (!savedTheme) {
      savedTheme = getSystemTheme();
      setIsSystemTheme(true);
    }

    setThemeState(savedTheme);
    setMounted(true);

    // Listen for system theme changes
    const mediaQuery = window.matchMedia("(prefers-color-scheme: dark)");
    const handleSystemThemeChange = (e: MediaQueryListEvent) => {
      if (isSystemTheme) {
        const newTheme: Theme = e.matches ? "dark" : "light";
        setThemeState(newTheme);
        setStoredTheme(newTheme);
      }
    };

    mediaQuery.addEventListener("change", handleSystemThemeChange);

    return () => {
      mediaQuery.removeEventListener("change", handleSystemThemeChange);
    };
  }, [isSystemTheme]);

  useEffect(() => {
    if (mounted) {
      // Update document class
      document.documentElement.classList.toggle("dark", theme === "dark");

      // Save to localStorage and cookie
      setStoredTheme(theme);

      // Update meta theme-color for mobile browsers
      const metaThemeColor = document.querySelector('meta[name="theme-color"]');
      if (metaThemeColor) {
        metaThemeColor.setAttribute("content", theme === "dark" ? "#111827" : "#ffffff");
      }
    }
  }, [theme, mounted]);

  const toggleTheme = () => {
    setThemeState((prev) => {
      const newTheme = prev === "light" ? "dark" : "light";
      setIsSystemTheme(false); // User explicitly chose a theme
      return newTheme;
    });
  };

  const setTheme = (newTheme: Theme) => {
    setThemeState(newTheme);
    setIsSystemTheme(false); // User explicitly chose a theme
  };

  // Always provide context, but hide content during hydration
  return (
    <ThemeContext.Provider value={{ theme, toggleTheme, setTheme, isSystemTheme }}>
      <div style={{ visibility: mounted ? "visible" : "hidden" }}>{children}</div>
    </ThemeContext.Provider>
  );
}

export function useTheme() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error("useTheme must be used within a ThemeProvider");
  }
  return context;
}
