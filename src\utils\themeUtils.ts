export type Theme = "light" | "dark";

// Theme persistence utilities
export const THEME_STORAGE_KEY = "theme";
export const THEME_COOKIE_NAME = "theme";

// Helper function to safely access localStorage
export const getStoredTheme = (): Theme | null => {
  try {
    if (typeof window !== "undefined") {
      const stored = localStorage.getItem(THEME_STORAGE_KEY) as Theme;
      return stored === "light" || stored === "dark" ? stored : null;
    }
  } catch (error) {
    console.warn("Failed to access localStorage:", error);
  }
  return null;
};

// Helper function to safely set localStorage
export const setStoredTheme = (theme: Theme): void => {
  try {
    if (typeof window !== "undefined") {
      localStorage.setItem(THEME_STORAGE_KEY, theme);
    }
  } catch (error) {
    console.warn("Failed to save theme to localStorage:", error);
  }
};

// Helper function to get theme from cookie
export const getThemeFromCookie = (): Theme | null => {
  try {
    if (typeof document !== "undefined") {
      const cookies = document.cookie.split(";");
      const themeCookie = cookies.find((cookie) => cookie.trim().startsWith(`${THEME_COOKIE_NAME}=`));
      if (themeCookie) {
        const theme = themeCookie.split("=")[1] as Theme;
        return theme === "light" || theme === "dark" ? theme : null;
      }
    }
  } catch (error) {
    console.warn("Failed to read theme from cookie:", error);
  }
  return null;
};

// Helper function to set theme cookie
export const setThemeCookie = (theme: Theme): void => {
  try {
    if (typeof document !== "undefined") {
      document.cookie = `${THEME_COOKIE_NAME}=${theme}; path=/; max-age=31536000; SameSite=Lax`;
    }
  } catch (error) {
    console.warn("Failed to set theme cookie:", error);
  }
};

// Helper function to detect system theme preference
export const getSystemTheme = (): Theme => {
  try {
    if (typeof window !== "undefined" && window.matchMedia) {
      return window.matchMedia("(prefers-color-scheme: dark)").matches ? "dark" : "light";
    }
  } catch (error) {
    console.warn("Failed to detect system theme:", error);
  }
  return "light";
};

// Helper function to apply theme to document
export const applyTheme = (theme: Theme): void => {
  try {
    if (typeof document !== "undefined") {
      // Update document class
      document.documentElement.classList.toggle("dark", theme === "dark");

      // Update meta theme-color for mobile browsers
      const metaThemeColor = document.querySelector('meta[name="theme-color"]');
      if (metaThemeColor) {
        metaThemeColor.setAttribute("content", theme === "dark" ? "#111827" : "#ffffff");
      }

      // Update document title with theme indicator (optional)
      const currentTitle = document.title;
      const baseTitle = currentTitle.replace(/ \[(Light|Dark)\]$/, "");
      document.title = `${baseTitle} [${theme === "dark" ? "Dark" : "Light"}]`;
    }
  } catch (error) {
    console.warn("Failed to apply theme:", error);
  }
};

// Helper function to get the best available theme
export const getBestTheme = (): Theme => {
  // 1. Try localStorage first
  const storedTheme = getStoredTheme();
  if (storedTheme) return storedTheme;

  // 2. Try cookie as fallback
  const cookieTheme = getThemeFromCookie();
  if (cookieTheme) return cookieTheme;

  // 3. Use system preference
  return getSystemTheme();
};

// Helper function to save theme to all storage methods
export const saveTheme = (theme: Theme): void => {
  setStoredTheme(theme);
  setThemeCookie(theme);
  applyTheme(theme);
};

// Helper function to check if theme is system preference
export const isSystemTheme = (): boolean => {
  const storedTheme = getStoredTheme();
  const cookieTheme = getThemeFromCookie();
  return !storedTheme && !cookieTheme;
};
