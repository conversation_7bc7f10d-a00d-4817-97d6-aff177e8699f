"use client";

import { useEffect, useState } from "react";
import dynamic from "next/dynamic";
import type { Icon } from "leaflet";

// Dynamically import the map components to avoid SSR issues
const MapContainer = dynamic(() => import("react-leaflet").then((mod) => mod.MapContainer), {
  ssr: false,
  loading: () => <div className="h-64 bg-gray-200 animate-pulse rounded-lg flex items-center justify-center">جاري تحميل الخريطة...</div>,
});

const TileLayer = dynamic(() => import("react-leaflet").then((mod) => mod.TileLayer), {
  ssr: false,
});

const Marker = dynamic(() => import("react-leaflet").then((mod) => mod.Marker), {
  ssr: false,
});

interface LocationMapProps {
  latitude?: number;
  longitude?: number;
  onLocationSelect: (lat: number, lng: number) => void;
  height?: string;
  isSelectable?: boolean;
}

// Component for handling map clicks
function MapClickHandler({ onLocationSelect }: { onLocationSelect: (lat: number, lng: number) => void }) {
  // eslint-disable-next-line @typescript-eslint/no-require-imports
  const { useMapEvents } = require("react-leaflet");
  useMapEvents({
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    click: (e: any) => {
      console.log("Map clicked:", e.latlng.lat, e.latlng.lng);
      onLocationSelect(e.latlng.lat, e.latlng.lng);
    },
  });
  return null;
}

export default function LocationMap({ latitude, longitude, onLocationSelect, height = "h-64", isSelectable = true }: LocationMapProps) {
  const [isClient, setIsClient] = useState(false);
  const [blueMarkerIcon, setBlueMarkerIcon] = useState<Icon | null>(null);

  useEffect(() => {
    setIsClient(true);
    // Create a custom blue marker icon for selection (only on client side)
    if (isClient) {
      import("leaflet").then((L) => {
        const icon = new L.default.Icon({
          iconUrl:
            "data:image/svg+xml;base64," +
            btoa(`
      <svg width="24" height="36" viewBox="0 0 24 36" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M12 0C5.373 0 0 5.373 0 12c0 10.5 12 24 12 24s12-13.5 12-24c0-6.627-5.373-12-12-12z" fill="#3b82f6"/>
        <circle cx="12" cy="12" r="6" fill="white"/>
        <circle cx="12" cy="12" r="3" fill="#3b82f6"/>
      </svg>
    `),
          iconSize: [24, 36],
          iconAnchor: [12, 36],
          popupAnchor: [0, -36],
        });
        setBlueMarkerIcon(icon);
      });
    }
  }, [isClient]);

  // Default coordinates for Palestine (center of the country)
  const defaultLat = latitude || 31.9522;
  const defaultLng = longitude || 35.2332;

  if (!isClient) {
    return <div className={`${height} bg-gray-200 animate-pulse rounded-lg flex items-center justify-center`}>جاري تحميل الخريطة...</div>;
  }

  return (
    <div className={`${height} rounded-lg overflow-hidden border border-gray-300`}>
      <MapContainer center={[defaultLat, defaultLng]} zoom={13} style={{ height: "100%", width: "100%" }} className="z-0">
        <TileLayer attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors' url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png" />

        {latitude && longitude && (blueMarkerIcon ? <Marker position={[latitude, longitude]} icon={blueMarkerIcon} /> : <Marker position={[latitude, longitude]} />)}

        {isSelectable && <MapClickHandler onLocationSelect={onLocationSelect} />}
      </MapContainer>

      {isSelectable && (
        <div className="p-3 bg-blue-50 border-t border-blue-200">
          <p className="text-sm text-blue-700 text-center">انقر على الخريطة لتحديد موقع العقار</p>
        </div>
      )}
    </div>
  );
}
