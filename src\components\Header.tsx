"use client";

import { useState } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useAuth } from "@/contexts/AuthContext";
import ThemeToggle from "./ThemeToggle";

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);
  const pathname = usePathname();
  const { user, userProfile, signOut } = useAuth();

  // Smooth scroll to top if on home page
  const handleHomeClick = (e: React.MouseEvent) => {
    if (pathname === "/") {
      e.preventDefault();
      window.scrollTo({ top: 0, behavior: "smooth" });
      // Clear the URL hash after scrolling to top
      setTimeout(() => {
        window.history.replaceState(null, "", window.location.pathname);
      }, 500);
      setIsMenuOpen(false);
    }
  };

  const handleSignOut = async () => {
    await signOut();
    setIsUserMenuOpen(false);
  };

  return (
    <header className="bg-[#1A341B] shadow-md sticky top-0 z-50 transition-colors duration-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <div className="flex items-center">
            <Link href="/" onClick={handleHomeClick}>
              <img src="/site-top-logo.png" alt="شعار دار كاسا" className="h-12 w-auto" />
            </Link>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex space-x-8 space-x-reverse text-lg">
            <Link href="/" className="text-white hover:text-green-400 transition-colors scroll-smooth" onClick={handleHomeClick}>
              الرئيسية
            </Link>
            <Link href="/#properties" className="text-white hover:text-green-400 transition-colors scroll-smooth">
              العقارات
            </Link>
            <Link href="/#about" className="text-white hover:text-green-400 transition-colors scroll-smooth">
              من نحن
            </Link>
            <Link href="/#contact" className="text-white hover:text-green-400 transition-colors scroll-smooth">
              اتصل بنا
            </Link>
          </nav>

          {/* CTA Buttons */}
          <div className="hidden md:flex items-center space-x-4 space-x-reverse">
            <ThemeToggle />
            {user ? (
              <div className="relative">
                <button
                  onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}
                  className="flex items-center space-x-2 space-x-reverse text-white hover:text-green-400 focus:outline-none"
                >
                  <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                  <span className="font-medium">{userProfile?.name || user.email}</span>
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </button>

                {isUserMenuOpen && (
                  <div className="absolute left-0 mt-2 w-48 bg-gray-800 rounded-md shadow-lg py-1 z-50 border border-gray-600">
                    <div className="px-4 py-2 text-sm text-white border-b border-gray-600">
                      <div className="font-medium">{userProfile?.name}</div>
                      <div className="text-gray-300">{user.email}</div>
                    </div>
                    <Link href="/dashboard" className="block w-full text-right px-4 py-2 text-sm text-white hover:bg-gray-700">
                      لوحة التحكم
                    </Link>
                    <button onClick={handleSignOut} className="block w-full text-right px-4 py-2 text-sm text-white hover:bg-gray-700">
                      تسجيل الخروج
                    </button>
                  </div>
                )}
              </div>
            ) : (
              <Link href="/signin">
                <button className="flex items-center space-x-2 space-x-reverse text-white hover:text-green-400 px-3 py-2 rounded-md text-sm font-medium">
                  <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z"
                    />
                  </svg>
                  <span>تسجيل الدخول</span>
                </button>
              </Link>
            )}
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden flex items-center space-x-2 space-x-reverse">
            <ThemeToggle />
            <button onClick={() => setIsMenuOpen(!isMenuOpen)} className="text-white hover:text-green-400 focus:outline-none">
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                {isMenuOpen ? (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                ) : (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                )}
              </svg>
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-gray-800 border-t border-gray-600 mx-10">
              <Link href="/" className="block px-3 py-2 text-white hover:text-green-400" onClick={handleHomeClick}>
                الرئيسية
              </Link>
              <Link href="/#properties" className="block px-3 py-2 text-white hover:text-green-400">
                العقارات
              </Link>
              <Link href="/#about" className="block px-3 py-2 text-white hover:text-green-400">
                من نحن
              </Link>
              <Link href="/#contact" className="block px-3 py-2 text-white hover:text-green-400">
                اتصل بنا
              </Link>

              {user ? (
                <div className="mt-4 pt-4 border-t border-gray-600">
                  <div className="px-3 py-2 text-sm text-white">
                    <div className="font-medium">{userProfile?.name}</div>
                    <div className="text-gray-300">{user.email}</div>
                  </div>
                  <Link href="/dashboard" className="block w-full mt-2 px-3 py-2 text-sm text-white hover:bg-gray-700 text-right">
                    لوحة التحكم
                  </Link>
                  <button onClick={handleSignOut} className="w-full mt-2 px-3 py-2 text-sm text-white hover:bg-gray-700 text-right">
                    تسجيل الخروج
                  </button>
                </div>
              ) : (
                <Link href="/signin">
                  <button className="w-full mt-4 flex items-center justify-center space-x-2 space-x-reverse text-white hover:text-green-400 px-3 py-2 rounded-md text-sm font-medium">
                    <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z"
                      />
                    </svg>
                    <span>تسجيل الدخول</span>
                  </button>
                </Link>
              )}
            </div>
          </div>
        )}
      </div>
      <style jsx global>{`
        html {
          scroll-behavior: smooth;
        }
      `}</style>
    </header>
  );
}
