import OpenAI from 'openai';
import { ChatMessage, UserIntent, PropertyMatch, Property } from '@/types/chat';

// Initialize OpenAI client
const apiKey = process.env.NEXT_PUBLIC_OPENAI_API_KEY;

if (!apiKey || apiKey === 'your_openai_api_key_here') {
  throw new Error('OpenAI API key is missing. Please add NEXT_PUBLIC_OPENAI_API_KEY to your .env.local file');
}

const openai = new OpenAI({
  apiKey,
  dangerouslyAllowBrowser: true, // Note: In production, use a backend proxy
});

export interface AIResponse {
  message: string;
  intent: UserIntent;
  suggestedProperties?: PropertyMatch[];
  followUpQuestions?: string[];
}

export class OpenAIService {
  private static instance: OpenAIService;
  private conversationHistory: ChatMessage[] = [];

  static getInstance(): OpenAIService {
    if (!OpenAIService.instance) {
      OpenAIService.instance = new OpenAIService();
    }
    return OpenAIService.instance;
  }

  async processUserMessage(
    userMessage: string,
    availableProperties: Property[]
  ): Promise<AIResponse> {
    try {
      // Add user message to history
      this.conversationHistory.push({
        id: Date.now().toString(),
        content: userMessage,
        type: 'user',
        timestamp: new Date(),
      });

      // Create system prompt for property search
      const systemPrompt = this.createSystemPrompt(availableProperties);
      
      // Get AI response
      const completion = await openai.chat.completions.create({
        model: "gpt-3.5-turbo",
        messages: [
          { role: "system", content: systemPrompt },
          ...this.conversationHistory.map(msg => ({
            role: msg.type === 'user' ? 'user' : 'assistant',
            content: msg.content
          }))
        ],
        max_tokens: 500,
        temperature: 0.7,
      });

      const aiResponse = completion.choices[0]?.message?.content || 'I apologize, I could not process your request.';
      
      // Parse AI response and extract intent
      const intent = await this.extractIntent(userMessage, aiResponse);
      
      // Find matching properties based on intent
      const suggestedProperties = await this.findMatchingProperties(intent, availableProperties);
      
      // Generate follow-up questions
      const followUpQuestions = this.generateFollowUpQuestions(intent, suggestedProperties);

      // Add AI response to history
      this.conversationHistory.push({
        id: (Date.now() + 1).toString(),
        content: aiResponse,
        type: 'assistant',
        timestamp: new Date(),
        metadata: {
          properties: suggestedProperties,
          intent: intent.action,
          confidence: intent.confidence,
        },
      });

      return {
        message: aiResponse,
        intent,
        suggestedProperties,
        followUpQuestions,
      };
    } catch (error) {
      console.error('Error processing user message:', error);
      return {
        message: 'I apologize, but I encountered an error. Please try again or contact support.',
        intent: {
          action: 'help',
          criteria: {},
          confidence: 0,
        },
        followUpQuestions: ['How can I help you find a property?'],
      };
    }
  }

  private createSystemPrompt(availableProperties: Property[]): string {
    return `You are an AI real estate assistant for DarCasa, a property website in Italy. Your role is to help users find properties that match their needs.

Available property types: ${[...new Set(availableProperties.map(p => p.type))].join(', ')}
Available cities: ${[...new Set(availableProperties.map(p => p.city))].join(', ')}

Guidelines:
1. Be helpful, friendly, and professional
2. Ask clarifying questions when needed
3. Provide specific property recommendations when possible
4. ALWAYS respond in Arabic only - do not use English
5. Focus on helping users find their ideal property
6. Be concise but informative
7. Use formal Arabic (فصحى) with some colloquial expressions when appropriate

Current conversation context: ${this.conversationHistory.length} messages exchanged.`;
  }

  private async extractIntent(userMessage: string, aiResponse: string): Promise<UserIntent> {
    try {
      const completion = await openai.chat.completions.create({
        model: "gpt-3.5-turbo",
        messages: [
          {
            role: "system",
            content: `Extract user intent from the message. Return a JSON object with:
            {
              "action": "search|information|recommendation|help",
              "criteria": {
                "location": "city name if mentioned",
                "priceRange": {"min": number, "max": number} if mentioned,
                "propertyType": "type if mentioned",
                "bedrooms": number if mentioned,
                "bathrooms": number if mentioned,
                "area": {"min": number, "max": number} if mentioned,
                "features": ["array of features mentioned"]
              },
              "confidence": number between 0 and 1
            }`
          },
          { role: "user", content: userMessage }
        ],
        max_tokens: 200,
        temperature: 0.1,
      });

      const intentText = completion.choices[0]?.message?.content || '{}';
      return JSON.parse(intentText);
    } catch (error) {
      console.error('Error extracting intent:', error);
      return {
        action: 'help',
        criteria: {},
        confidence: 0.5,
      };
    }
  }

  private async findMatchingProperties(intent: UserIntent, availableProperties: Property[]): Promise<PropertyMatch[]> {
    if (intent.action !== 'search' && intent.action !== 'recommendation') {
      return [];
    }

    const matches: PropertyMatch[] = [];

    for (const property of availableProperties) {
      let score = 0;
      const reasons: string[] = [];

      // Location matching
      if (intent.criteria.location && 
          property.city.toLowerCase().includes(intent.criteria.location.toLowerCase())) {
        score += 30;
        reasons.push(`Located in ${property.city}`);
      }

      // Price matching
      if (intent.criteria.priceRange) {
        if (property.price >= intent.criteria.priceRange.min && 
            property.price <= intent.criteria.priceRange.max) {
          score += 25;
          reasons.push(`Price within your budget (€${property.price.toLocaleString()})`);
        }
      }

      // Property type matching
      if (intent.criteria.propertyType && 
          property.type === intent.criteria.propertyType) {
        score += 20;
        reasons.push(`Matches your preferred property type`);
      }

      // Bedrooms matching
      if (intent.criteria.bedrooms && 
          property.bedrooms >= intent.criteria.bedrooms) {
        score += 15;
        reasons.push(`${property.bedrooms} bedrooms meet your needs`);
      }

      // Bathrooms matching
      if (intent.criteria.bathrooms && 
          property.bathrooms >= intent.criteria.bathrooms) {
        score += 10;
        reasons.push(`${property.bathrooms} bathrooms meet your needs`);
      }

      if (score > 0) {
        matches.push({
          property,
          score,
          reasons,
          aiExplanation: this.generatePropertyExplanation(property, reasons),
        });
      }
    }

    // Sort by score and return top matches
    return matches
      .sort((a, b) => b.score - a.score)
      .slice(0, 5);
  }

  private generatePropertyExplanation(property: Property, reasons: string[]): string {
    return `This ${property.type} in ${property.city} is a great match because: ${reasons.join(', ')}. It features ${property.bedrooms} bedrooms, ${property.bathrooms} bathrooms, and ${property.area}m² of space.`;
  }

  private generateFollowUpQuestions(intent: UserIntent, suggestedProperties: PropertyMatch[]): string[] {
    const questions: string[] = [];

    if (suggestedProperties.length === 0) {
      questions.push('Could you tell me more about your preferences?');
      questions.push('What is your budget range?');
      questions.push('Which areas are you interested in?');
    } else if (suggestedProperties.length < 3) {
      questions.push('Would you like me to expand the search criteria?');
      questions.push('Are you flexible on any of your requirements?');
    } else {
      questions.push('Would you like to see more details about any of these properties?');
      questions.push('Should I refine the search further?');
    }

    return questions;
  }

  clearHistory(): void {
    this.conversationHistory = [];
  }

  getHistory(): ChatMessage[] {
    return [...this.conversationHistory];
  }
}

export default OpenAIService.getInstance();
