{"name": "cursor-da<PERSON><PERSON>", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "export": "next build", "deploy": "npm run export && firebase deploy --only hosting"}, "dependencies": {"@langchain/community": "^0.3.53", "@langchain/openai": "^0.6.9", "@types/leaflet": "^1.9.20", "@types/uuid": "^10.0.0", "date-fns": "^4.1.0", "firebase": "^12.0.0", "framer-motion": "^11.18.2", "langchain": "^0.3.31", "leaflet": "^1.9.4", "lucide-react": "^0.541.0", "next": "15.3.4", "openai": "^5.15.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-leaflet": "^5.0.0", "react-markdown": "^10.1.0", "react-syntax-highlighter": "^15.6.3", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.3.4", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "^5"}}