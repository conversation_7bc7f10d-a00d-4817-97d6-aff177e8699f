import { initializeApp } from "firebase/app";
import { getFirestore } from "firebase/firestore";
import { getStorage } from "firebase/storage";
import { getAuth } from "firebase/auth";

const firebaseConfig = {
  apiKey: "AIzaSyCnlY2Vk5yYmOrK6TeFQYluyAUgllSvzKY",
  authDomain: "dar-casa.firebaseapp.com",
  projectId: "dar-casa",
  storageBucket: "dar-casa.firebasestorage.app",
  messagingSenderId: "484285471144",
  appId: "1:484285471144:web:001e7e9a7dee445c69e59d",
  measurementId: "G-KQ12KB03JR",
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firestore
export const db = getFirestore(app);

// Initialize Storage
export const storage = getStorage(app);

// Initialize Auth
export const auth = getAuth(app);

export default app;
