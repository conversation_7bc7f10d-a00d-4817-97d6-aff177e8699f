"use client";

import { Property } from "./Property";
import { formatPrice, formatArea } from "@/utils/formatters";
import Link from "next/link";
import { motion } from "framer-motion";
import { getPropertyTypeText } from "./PropertyType";

interface PropertyCardProps {
  property: Property;
  variant?: "default" | "compact" | "featured";
  showNewBadge?: boolean;
  showTypeBadge?: boolean;
  showFeaturedBadge?: boolean;
  showPropertyDetails?: boolean;
  className?: string;
  index?: number;
  animate?: boolean;
}

export default function PropertyCard({
  property,
  variant = "default",
  showNewBadge = false,
  showTypeBadge = true,
  showFeaturedBadge = true,
  showPropertyDetails = true,
  className = "",
  index = 0,
  animate = true,
}: PropertyCardProps) {
  const getVariantClasses = () => {
    switch (variant) {
      case "compact":
        return "h-44"; // Smaller height for compact view
      case "featured":
        return "h-52"; // Larger height for featured view
      default:
        return "h-48"; // Standard height
    }
  };

  const cardContent = (
    <div className={`card overflow-hidden ${className}`}>
      <div className="relative">
        <img src={property.images[0]} alt={property.title} className={`w-full ${getVariantClasses()} object-cover`} />

        {/* Badges */}
        <div className="absolute top-4 left-4 flex flex-col gap-2">
          {showTypeBadge && <div className="bg-green-600 text-white px-3 py-1 rounded-full text-sm font-medium">{getPropertyTypeText(property.type)}</div>}
          {showNewBadge && <div className="bg-green-600 text-white px-3 py-1 rounded-full text-sm font-medium">جديد</div>}
        </div>

        {showFeaturedBadge && property.featured && (
          <div className="absolute top-4 right-4 bg-gradient-to-r from-yellow-400 via-yellow-500 to-amber-500 text-white px-3 py-1 rounded-full text-sm font-medium shadow-lg">
            مميز
          </div>
        )}
      </div>

      <div className="p-5">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-1">{property.title}</h3>

        <p className="text-gray-600 dark:text-gray-300 mb-2 flex items-center">
          <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
          {property.location}
        </p>

        {showPropertyDetails && (
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center space-x-4 space-x-reverse">
              <span className="text-gray-600 dark:text-gray-400 text-sm flex items-center">
                <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
                  />
                </svg>
                {formatArea(property.area)}
              </span>
              <span className="text-gray-600 dark:text-gray-400 text-sm flex items-center">
                <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 5a2 2 0 012-2h4a2 2 0 012 2v2H8V5z" />
                </svg>
                {property.bedrooms}
              </span>
              <span className="text-gray-600 dark:text-gray-400 text-sm flex items-center">
                <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 14v3m4-3v3m4-3v3M3 21h18M3 10h18M3 7l9-4 9 4M4 10h16v11H4V10z" />
                </svg>
                {property.bathrooms}
              </span>
            </div>
          </div>
        )}

        <div className="flex justify-between items-center mb-3">
          <span className="text-2xl font-bold text-green-600 dark:text-green-400">{formatPrice(property.price)}</span>
        </div>

        <Link href={`/property/${property.nid}`} className="btn-primary w-full inline-block text-center">
          عرض التفاصيل
        </Link>
      </div>
    </div>
  );

  if (!animate) {
    return cardContent;
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 40 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true, amount: 0.1, margin: "0px 0px -20% 0px" }}
      transition={{ duration: 0.6, delay: index * 0.12, ease: "easeOut" }}
    >
      {cardContent}
    </motion.div>
  );
}
