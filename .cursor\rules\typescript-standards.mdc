# TypeScript Coding Standards

## Type Definitions

- Always use explicit types for function parameters and return values
- Use the Property type from [src/components/Property.ts](mdc:src/components/Property.ts) for real estate data
- Prefer interfaces over types for object shapes
- Use union types for props that can accept multiple values

## Import/Export Patterns

- Use named exports for components and utilities
- Use default exports for pages and layouts
- Import types with `import type` syntax
- Use path aliases (`@/*`) for imports from src directory

## React Component Standards

- Use functional components with hooks
- Always type props interfaces
- Use React.FC for simple components, explicit typing for complex ones
- Prefer destructuring props in function parameters

## Error Handling

- Use try-catch blocks for async operations
- Handle Firebase errors gracefully
- Provide meaningful error messages in Arabic when possible

## Code Organization

- Keep components focused and single-purpose
- Extract reusable logic into custom hooks
- Use TypeScript strict mode (enabled in [tsconfig.json](mdc:tsconfig.json))
- Prefer const assertions for static data

## Naming Conventions

- Use PascalCase for components and interfaces
- Use camelCase for variables, functions, and properties
- Use UPPER_SNAKE_CASE for constants
- Use descriptive names that reflect Arabic real estate context
  description:
  globs:
  alwaysApply: false

---
