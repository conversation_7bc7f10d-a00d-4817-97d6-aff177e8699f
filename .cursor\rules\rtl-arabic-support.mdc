# RTL and Arabic Language Support

## Layout Configuration

- Root layout configured for RTL in [src/app/layout.tsx](mdc:src/app/layout.tsx)
- Use `dir="rtl"` and `lang="ar"` attributes
- <PERSON><PERSON><PERSON> font configured for Arabic and Latin support

## CSS and Styling

- Use RTL-aware Tailwind classes (e.g., `text-right`, `mr-4` instead of `ml-4`)
- Implement proper text alignment for Arabic content
- Use appropriate font weights for Arabic text
- Ensure proper spacing and padding for RTL layout

## Content Localization

- All user-facing text should be in Arabic
- Property types, locations, and descriptions in Arabic
- Error messages and notifications in Arabic
- Form labels and placeholders in Arabic

## Number and Date Formatting

- Use Arabic numeral system where appropriate
- Format dates according to Arabic locale
- Use proper currency formatting for Saudi Riyal (SAR)
- Implement proper number formatting utilities

## Input and Form Handling

- Support Arabic text input
- Implement proper text direction for form fields
- Use appropriate input types for Arabic content
- Handle Arabic text validation and sanitization

## Map and Location Features

- Display location names in Arabic
- Use Arabic place names in map markers
- Implement Arabic search functionality
- Support Arabic address formatting

## Accessibility for Arabic Users

- Ensure proper screen reader support for Arabic
- Use appropriate ARIA labels in Arabic
- Implement proper focus management for RTL layout
- Support Arabic keyboard navigation patterns
  description:
  globs:
  alwaysApply: false

---
