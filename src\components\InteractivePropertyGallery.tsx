"use client";

import { useState } from "react";
import { Property} from "./Property";
import { formatPrice } from "@/utils/formatters";
import { motion } from "framer-motion";
import { PropertyType, PropertyTypes } from "./PropertyType";

interface InteractivePropertyGalleryProps {
  property: Property;
}

export default function InteractivePropertyGallery({ property }: InteractivePropertyGalleryProps) {
  const getPropertyTypeText = (typeCode: string) => {
    const type = PropertyTypes.find((t: PropertyType) => t.code === typeCode);
    return type ? type.name_ar : typeCode;
  };

  const [selectedImageIndex, setSelectedImageIndex] = useState(0);

  return (
    <>
      {/* Hero Section with Image Gallery */}
      <motion.div
        className="relative"
        initial={{ opacity: 0, y: 40 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true, amount: 0.3 }}
        transition={{ duration: 0.8, ease: "easeOut" }}
      >
        <img src={property.images[selectedImageIndex]} alt={property.title} className="w-full h-96 object-cover" />
        {property.featured && <div className="absolute top-6 right-6 bg-red-500 text-white px-4 py-2 rounded-full text-sm font-medium">مميز</div>}
        <div className="absolute top-6 left-6 bg-green-600 text-white px-4 py-2 rounded-full text-sm font-medium">{getPropertyTypeText(property.type)}</div>
        <div className="absolute bottom-6 left-6 bg-white bg-opacity-90 backdrop-blur-sm px-6 py-3 rounded-xl">
          <div className="text-3xl font-bold text-green-600">{formatPrice(property.price)}</div>
        </div>
      </motion.div>

      {/* Image Gallery */}
      {property.images.length > 1 && (
        <div className="p-8 pt-0">
          <h3 className="text-xl font-semibold text-gray-900 mb-4">معرض الصور</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {property.images.map((image, index) => (
              <motion.div
                key={index}
                className={`relative group cursor-pointer border-2 rounded-lg transition-all duration-200 ${
                  selectedImageIndex === index ? "border-green-500 scale-105" : "border-transparent hover:border-green-300"
                }`}
                onClick={() => setSelectedImageIndex(index)}
                initial={{ opacity: 0, y: 40 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true, amount: 0.3 }}
                transition={{ duration: 0.5, delay: index * 0.12, ease: "easeOut" }}
              >
                <img
                  src={image}
                  alt={`${property.title} - صورة ${index + 1}`}
                  className="w-full h-32 object-cover rounded-lg transition-transform duration-200 group-hover:scale-105"
                />
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200 rounded-lg flex items-center justify-center">
                  <svg className="w-8 h-8 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
                  </svg>
                </div>
                {selectedImageIndex === index && (
                  <div className="absolute top-2 right-2 bg-green-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs">
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                )}
              </motion.div>
            ))}
          </div>
        </div>
      )}
    </>
  );
}
