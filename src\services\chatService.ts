import { ChatMessage, PropertyMatch, AIResponse } from "@/types/chat";
import openaiService from "./openaiService";
import { Property } from "@/components/Property";

export interface ChatState {
  messages: ChatMessage[];
  isLoading: boolean;
  isOpen: boolean;
  suggestedProperties: PropertyMatch[];
  followUpQuestions: string[];
}

export class ChatService {
  private static instance: ChatService;
  private state: ChatState = {
    messages: [],
    isLoading: false,
    isOpen: false,
    suggestedProperties: [],
    followUpQuestions: [],
  };

  private listeners: ((state: ChatState) => void)[] = [];

  static getInstance(): ChatService {
    if (!ChatService.instance) {
      ChatService.instance = new ChatService();
    }
    return ChatService.instance;
  }

  // Initialize chat with welcome message
  initializeChat(): void {
    if (this.state.messages.length === 0) {
      const welcomeMessage: ChatMessage = {
        id: "welcome",
        content: "مرحباً! أنا مساعدك الذكي للعقارات. كيف يمكنني مساعدتك في العثور على العقار المثالي؟",
        type: "assistant",
        timestamp: new Date(),
        metadata: {
          intent: "help",
          confidence: 1,
        },
      };

      this.addMessage(welcomeMessage);
      this.setFollowUpQuestions([
        "أخبرني عن نوع العقار الذي تبحث عنه",
        "ما هو ميزانيتك؟",
        "في أي مدينة تريد البحث؟",
        "كم عدد غرف النوم التي تحتاجها؟",
        "هل تفضل شقة أم فيلا؟",
      ]);
    }
  }

  // Add a message to the chat
  addMessage(message: ChatMessage): void {
    this.state.messages.push(message);
    this.notifyListeners();
  }

  // Send a user message and get AI response
  async sendMessage(message: string, availableProperties: Property[]): Promise<void> {
    // Add user message
    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      content: message,
      type: "user",
      timestamp: new Date(),
    };

    this.addMessage(userMessage);
    this.setLoading(true);

    try {
      // Get AI response
      const aiResponse: AIResponse = await openaiService.processUserMessage(message, availableProperties);

      // Add AI response
      const assistantMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        content: aiResponse.message,
        type: "assistant",
        timestamp: new Date(),
        metadata: {
          properties: aiResponse.suggestedProperties,
          intent: aiResponse.intent.action,
          confidence: aiResponse.intent.confidence,
        },
      };

      this.addMessage(assistantMessage);

      // Update suggested properties and follow-up questions
      if (aiResponse.suggestedProperties) {
        this.setSuggestedProperties(aiResponse.suggestedProperties);
      }

      if (aiResponse.followUpQuestions) {
        this.setFollowUpQuestions(aiResponse.followUpQuestions);
      }
    } catch (error) {
      console.error("Error sending message:", error);

      // Add error message
      const errorMessage: ChatMessage = {
        id: (Date.now() + 2).toString(),
        content: "عذراً، حدث خطأ. يرجى المحاولة مرة أخرى.\n\nSorry, an error occurred. Please try again.",
        type: "assistant",
        timestamp: new Date(),
        metadata: {
          intent: "help",
          confidence: 0,
        },
      };

      this.addMessage(errorMessage);
    } finally {
      this.setLoading(false);
    }
  }

  // Set loading state
  setLoading(isLoading: boolean): void {
    this.state.isLoading = isLoading;
    this.notifyListeners();
  }

  // Toggle chat open/close
  toggleChat(): void {
    this.state.isOpen = !this.state.isOpen;
    this.notifyListeners();
  }

  // Open chat
  openChat(): void {
    this.state.isOpen = true;
    this.notifyListeners();
  }

  // Close chat
  closeChat(): void {
    this.state.isOpen = false;
    this.notifyListeners();
  }

  // Set suggested properties
  setSuggestedProperties(properties: PropertyMatch[]): void {
    this.state.suggestedProperties = properties;
    this.notifyListeners();
  }

  // Set follow-up questions
  setFollowUpQuestions(questions: string[]): void {
    this.state.followUpQuestions = questions;
    this.notifyListeners();
  }

  // Clear chat history
  clearChat(): void {
    this.state.messages = [];
    this.state.suggestedProperties = [];
    this.state.followUpQuestions = [];
    openaiService.clearHistory();
    this.notifyListeners();
  }

  // Get current state
  getState(): ChatState {
    return { ...this.state };
  }

  // Subscribe to state changes
  subscribe(listener: (state: ChatState) => void): () => void {
    this.listeners.push(listener);

    // Return unsubscribe function
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  // Notify all listeners of state changes
  private notifyListeners(): void {
    this.listeners.forEach((listener) => listener(this.getState()));
  }

  // Get chat statistics
  getChatStats(): { totalMessages: number; userMessages: number; assistantMessages: number } {
    const totalMessages = this.state.messages.length;
    const userMessages = this.state.messages.filter((m) => m.type === "user").length;
    const assistantMessages = this.state.messages.filter((m) => m.type === "assistant").length;

    return {
      totalMessages,
      userMessages,
      assistantMessages,
    };
  }

  // Export chat history
  exportChatHistory(): string {
    return this.state.messages.map((msg) => `${msg.type === "user" ? "User" : "Assistant"}: ${msg.content}`).join("\n\n");
  }
}

export default ChatService.getInstance();
