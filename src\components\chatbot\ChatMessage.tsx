"use client";

import { ChatMessage as ChatMessageType } from "@/types/chat";
import { formatDistanceToNow } from "date-fns";
import { ar, enUS } from "date-fns/locale";
import { motion } from "framer-motion";

interface ChatMessageProps {
  message: ChatMessageType;
  isLast: boolean;
}

export default function ChatMessage({ message, isLast }: ChatMessageProps) {
  const isUser = message.type === "user";
  const isArabic = /[\u0600-\u06FF]/.test(message.content);

  const formatTime = (date: Date) => {
    try {
      return formatDistanceToNow(date, {
        addSuffix: true,
        locale: isArabic ? ar : enUS,
      });
    } catch {
      return "الآن";
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={`flex ${isUser ? "justify-end" : "justify-start"} mb-6`}
    >
      <div
        className={`max-w-[75%] rounded-2xl px-5 py-4 shadow-sm ${
          isUser
            ? "bg-green-600 text-white rounded-br-md"
            : "bg-white dark:bg-gray-800 text-gray-900 dark:text-white border border-gray-200 dark:border-gray-700 rounded-bl-md"
        }`}
      >
        {/* Message content */}
        <div
          className={`whitespace-pre-wrap leading-relaxed ${
            isArabic ? "text-right" : "text-left"
          }`}
        >
          {message.content}
        </div>

        {/* Timestamp */}
        <div
          className={`text-xs mt-3 opacity-70 ${
            isUser ? "text-green-100" : "text-gray-500 dark:text-gray-400"
          } ${isUser ? "text-right" : "text-left"}`}
        >
          {formatTime(message.timestamp)}
        </div>

        {/* AI Confidence indicator for assistant messages */}
        {!isUser && message.metadata?.confidence !== undefined && (
          <div className="flex items-center mt-3 space-x-2 space-x-reverse">
            <div className="text-xs text-gray-500 dark:text-gray-400 font-medium">
              درجة الثقة: {Math.round(message.metadata.confidence * 100)}%
            </div>
            <div className="w-20 h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
              <div
                className="h-full bg-green-500 transition-all duration-500 rounded-full"
                style={{ width: `${message.metadata.confidence * 100}%` }}
              />
            </div>
          </div>
        )}
      </div>

      {/* User avatar */}
      {isUser && (
        <div className="ml-3 flex-shrink-0">
          <div className="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center">
            <svg
              className="w-5 h-5 text-white"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fillRule="evenodd"
                d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
                clipRule="evenodd"
              />
            </svg>
          </div>
        </div>
      )}

      {/* Assistant avatar */}
      {!isUser && (
        <div className="mr-3 flex-shrink-0">
          <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
            <svg
              className="w-5 h-5 text-white"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
        </div>
      )}
    </motion.div>
  );
}
