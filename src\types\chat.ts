export interface ChatMessage {
  id: string;
  content: string;
  type: 'user' | 'assistant';
  timestamp: Date;
  metadata?: {
    properties?: PropertyMatch[];
    intent?: string;
    confidence?: number;
  };
}

export interface PropertyMatch {
  property: Property;
  score: number;
  reasons: string[];
  aiExplanation: string;
}

export interface UserIntent {
  action: 'search' | 'information' | 'recommendation' | 'help';
  criteria: {
    location?: string;
    priceRange?: { min: number; max: number };
    propertyType?: string;
    bedrooms?: number;
    bathrooms?: number;
    area?: { min: number; max: number };
    features?: string[];
  };
  confidence: number;
}

export interface ChatSession {
  id: string;
  messages: ChatMessage[];
  userPreferences: {
    preferredLocations: string[];
    preferredPropertyTypes: string[];
    budgetRange: { min: number; max: number };
    familySize: number;
  };
  createdAt: Date;
  lastActive: Date;
}

// Import Property type from existing component
export interface Property {
  nid: number;
  id: string;
  title: string;
  location: string;
  country: string;
  city: string;
  region: string;
  price: number;
  type: string;
  bedrooms: number;
  bathrooms: number;
  area: number;
  description: string;
  parking: string;
  images: string[];
  featured: boolean;
  date: string;
  latitude?: number;
  longitude?: number;
}
