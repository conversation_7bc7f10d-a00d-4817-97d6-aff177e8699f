"use client";

import { motion } from "framer-motion";

export default function About() {
  return (
    <motion.section
      id="about"
      className="py-16 bg-white dark:bg-gray-900 transition-colors duration-200"
      initial={{ opacity: 0, y: 40 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true, amount: 0.3 }}
      transition={{ duration: 0.8, ease: "easeOut" }}
    >
      <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-10">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">من نحن</h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
            دار كاسا هي منصة عقارية رائدة تهدف إلى تسهيل عملية البحث، البيع، والشراء للعقارات في جميع أنحاء المملكة. نؤمن بأن لكل شخص الحق في إيجاد المسكن المثالي أو الاستثمار
            العقاري المناسب بسهولة وشفافية.
          </p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
          <motion.div
            initial={{ opacity: 0, y: 40 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.7, delay: 0.1, ease: "easeOut" }}
          >
            <h3 className="text-2xl font-semibold text-green-700 dark:text-green-400 mb-3">رؤيتنا</h3>
            <p className="text-gray-700 dark:text-gray-300 mb-6">
              أن نكون الخيار الأول في المنظقة للبحث عن العقارات، من خلال تقديم تجربة رقمية متكاملة تجمع بين الحداثة والموثوقية.
            </p>
            <h3 className="text-2xl font-semibold text-green-700 dark:text-green-400 mb-3">رسالتنا</h3>
            <p className="text-gray-700 dark:text-gray-300">تمكين الأفراد والعائلات والمستثمرين من اتخاذ قرارات عقارية ذكية عبر توفير معلومات دقيقة، دعم متواصل، وخدمات مبتكرة.</p>
          </motion.div>
          <motion.div
            className="flex justify-center"
            initial={{ opacity: 0, y: 40 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.7, delay: 0.2, ease: "easeOut" }}
          >
            <img src="/about-illustration.svg" alt="عن دار كاسا" className="w-80 h-80 object-contain rounded-xl shadow-lg border border-gray-200 dark:border-gray-700" />
          </motion.div>
        </div>
      </div>
    </motion.section>
  );
}
